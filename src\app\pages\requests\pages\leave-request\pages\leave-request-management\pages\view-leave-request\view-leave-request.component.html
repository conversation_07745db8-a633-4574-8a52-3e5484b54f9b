<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="title">{{ isLeaveApproval === false ? 'Deny Absence Request' : isLeaveApproval === null ? 'Absence Request Details' : 'Approve Absence Request' }}</div>
    <div class="action-btn-wrapper">
      <button
        mat-raised-button
        color="accent"
        class="mat-accent-btn back-btn"
        type="button"
        (click)="closeViewSideNavFun()">
        Close
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : leaveDetails"></ng-container>
  </div>
</div>

<ng-template #leaveDetails>
    @if(isLeaveApproval) {
        <div class="leave-decision-wrapper">
            <div><img [src]="constants.staticImages.icons.checkCircle" height="70" width="70" alt="" /></div>
             <div class="decision-content">Approve Absence Request</div>
         </div>
    }
     @if(isLeaveApproval === false) {
         <div class="leave-decision-wrapper">
              <div><img [src]="constants.staticImages.icons.redCrossCircle" height="70" width="70" alt="" /></div>
             <div class="decision-content">Deny Absence Request</div>
          </div>
      }
    <div class="leave-wrapper">
        <div class="leave-details m-0">
          <div class="d-flex">
            <img [src]="constants.staticImages.icons.profileIcon" alt="" />
            <div class="title">Requested By</div>
          </div>
          <div class="content">{{ selectedLeaveDetails?.role }} <div class="dot d-inline-block me-1 ms-1"></div> <span class="primary-color ms-1">{{ selectedLeaveDetails?.requestedBy | titlecase }}</span> </div>
        </div>
        @if(isLeaveApproval === null) {
            <ng-container [ngTemplateOutlet]="leaveActions"></ng-container>
        }
    </div>
  <div class="leave-details">
    <div class="d-flex">
      <img [src]="constants.staticImages.icons.calendarIcon" alt="" />
      <div class="title">Leave Date</div>
    </div>
    <div class="content">
        <div>
            @if(selectedLeaveDetails?.leaveStartTime) {
               <div class="info"> {{ selectedLeaveDetails?.leaveStartDate | date }} <div class="dot d-inline-block"></div> {{ selectedLeaveDetails?.leaveStartTime | date: 'shortTime' }} - {{ selectedLeaveDetails?.leaveEndTime | date: 'shortTime' }} <span class="text-gray ms-1">({{ getHoursInRange(selectedLeaveDetails!.leaveStartTime, selectedLeaveDetails!.leaveEndTime) }} hours)</span></div>
            }
            @else {
                <div class="info"> {{ selectedLeaveDetails?.leaveStartDate | date }} - {{ selectedLeaveDetails?.leaveEndDate | date }} <span class="text-gray ms-1">({{ getDayInRange(selectedLeaveDetails!.leaveStartDate, selectedLeaveDetails!.leaveEndDate) }} day)</span></div>
              }
              <div class="requested-on">Requested on <span>{{ selectedLeaveDetails?.requestDate | date }}</span> </div>
        </div>
    </div>
  </div>
  <div class="leave-details">
    <div class="d-flex">
      <img [src]="constants.staticImages.icons.repeatType" alt="" />
      <div class="title">Leave Type</div>
    </div>
    <div class="content">{{ leaveTypes[selectedLeaveDetails!.leaveType] | titlecase  }} Leave</div>
  </div>
  <div class="leave-details">
    <div class="d-flex">
      <img [src]="constants.staticImages.icons.status" alt="" />
      <div class="title">Used Time Off Days</div>
    </div>
    <div class="content"> <span class="fw-bold me-1">{{ selectedLeaveDetails?.paidUsedLeaveDays }}</span> Days Paid Time Off <div class="dot d-inline-block"></div> <span class="fw-bold me-1">{{ selectedLeaveDetails?.unpaidUsedLeaveDays }}</span> Days Unpaid Time Off</div>
  </div>
  <div class="leave-details">
    <div class="d-flex">
      <img [src]="constants.staticImages.icons.questionnaire" alt="" />
      <div class="title">Leave Note</div>
    </div>
    <div class="content">{{ selectedLeaveDetails?.reason | titlecase }}</div>
  </div>
  @if(selectedLeaveDetails?.status === leaveStatus.APPROVED) {
      <div class="leave-details">
        <div class="d-flex">
          <img [src]="constants.staticImages.icons.arrowsCircle" alt="" />
          <div class="title">Substitute</div>
        </div>
        <div class="content">{{ selectedLeaveDetails?.substituteName ?? 'No Substitute Available' | titlecase }}</div>
      </div>
      <div class="leave-details">
        <div class="d-flex">
          <img [src]="constants.staticImages.icons.calendarIcon" alt="" />
          <div class="title">Approved By</div>
        </div>
        <div class="content">
            <div>
                <div>{{ selectedLeaveDetails?.approvedBy | titlecase }}</div>
                <div class="requested-on">Approved on <span>{{ selectedLeaveDetails?.approveDate | date }}</span></div>
            </div>
        </div>
      </div>
  }
  @if(selectedLeaveDetails?.status === leaveStatus.REJECTED) {
      <div class="leave-details">
        <div class="d-flex">
          <img [src]="constants.staticImages.icons.questionnaire" alt="" />
          <div class="title">Reject Reason</div>
        </div>
        <div class="content">{{ selectedLeaveDetails?.remark | titlecase }}</div>
      </div>
      <div class="leave-details">
        <div class="d-flex">
          <img [src]="constants.staticImages.icons.calendarIcon" alt="" />
          <div class="title">Rejected By</div>
        </div>
        <div class="content">
            <div>
                <div>{{ selectedLeaveDetails?.approvedBy | titlecase }}</div>
                <div class="requested-on">Rejected on <span>{{ selectedLeaveDetails?.approveDate | date }}</span></div>
            </div>
        </div>
      </div>
  }
  @if(isLeaveApproval === false) {
      <ng-container [ngTemplateOutlet]="rejectReason"></ng-container>
  }
  @if(isLeaveApproval || selectedLeaveDetails?.status === leaveStatus.APPROVED) {
    <ng-container [ngTemplateOutlet]="substituteDropDown"></ng-container>
  }
</ng-template>

<ng-template #rejectReason>
    <div class="dotted-divider"></div>
    <form [formGroup]="rejectRequestForm">
        <mat-form-field class="mt-3">
          <mat-label>Request reject reason</mat-label>
          <textarea
            matInput
            formControlName="remark"
            cdkTextareaAutosize
            cdkAutosizeMinRows="3"
            cdkAutosizeMaxRows="10"></textarea>
          <mat-error>
            <app-error-messages [control]="rejectRequestForm.controls.remark"></app-error-messages>
          </mat-error>
        </mat-form-field>
        <div class="text-center">
            <button
            mat-raised-button
            color="accent"
            class="mat-accent-btn back-btn"
            type="button"
            (click)="closeViewSideNavFun()">
            Cancel
           </button>
            <button
            mat-raised-button
            color="warn"
            class="mat-red-btn back-btn ms-2"
            type="button"
            [appLoader]="showBtnLoader"
            (click)="onRejectOrApproveLeave(leaveStatus.REJECTED)">
            Reject
          </button>
        </div>
    </form>
</ng-template>

<ng-template #substituteDropDown>
    <div class="dotted-divider"></div>
    <form [formGroup]="rejectRequestForm">
        <div class="field-wrapper field-with-mat-inputs">
          @if (!isLeaveApproval) {
            <label>Find a substitute who will be available on this day</label>
            <mat-form-field>
                <mat-label>Select substitute</mat-label>
                <mat-select class="mat-select-custom" formControlName="substituteDetailsId">
                    @if (substituteLists && substituteLists.length) {
                        <mat-option
                          *ngFor="let substituteList of substituteLists"
                          [value]="substituteList?.id">
                          <div class="instructor-list">
                            <div class="instructor-name">{{ substituteList?.name }}</div>
                          </div>
                        </mat-option>
                      } @else {
                        <mat-option>No substitute available</mat-option>
                      }
                </mat-select>
            </mat-form-field>
          }
            <div class="text-center">
                <button
                mat-raised-button
                color="accent"
                class="mat-accent-btn back-btn"
                type="button"
                (click)="closeViewSideNavFun()">
                Cancel
               </button>
                <button
                mat-raised-button
                color="primary"
                class="mat-primary-btn back-btn ms-2"
                type="button"
                [appLoader]="showBtnLoader"
                (click)="isLeaveApproval ? onRejectOrApproveLeave(leaveStatus.APPROVED) : assignSubstitute()">
                {{ isLeaveApproval ? 'Approve' : 'Assign Substitute' }}
              </button>
            </div>
        </div>
    </form>
</ng-template>

<ng-template #leaveActions>
  @if (currentUser?.userRole === constants.roles.ADMIN && selectedLeaveDetails?.status === leaveStatus.PENDING_ACTION) {
    <div class="icons">
      <img
        (click)="getLeaveFn(true)"
        matTooltip="Approve" alt=""
        [src]="constants.staticImages.icons.checkCircle"
        class="approve me-3" />
      <img
      (click)="getLeaveFn(false)"
        matTooltip="Reject" alt=""
        [src]="constants.staticImages.icons.crossCircle"
        class="cancel" />
    </div>
  }
  @else {
    @switch (selectedLeaveDetails?.status) {
        @case (leaveStatus.PENDING_ACTION) {
            <div class="status onHold"><img [src]="constants.staticImages.icons.timeCircleClock"
            class="yellow-filter me-1" alt="" /> Pending</div>
        }
        @case (leaveStatus.APPROVED) {
          <div class="status enrolled"><img [src]="constants.staticImages.icons.checkCircle"
            class="me-1" alt="" /> Approved</div>
        }
        @case (leaveStatus.REJECTED) {
          <div class="status canceled"><img [src]="constants.staticImages.icons.redCrossCircle" class="reject me-1" alt="" /> Rejected</div>
        }
      }
  }
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
