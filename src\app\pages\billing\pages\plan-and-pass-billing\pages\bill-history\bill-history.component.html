<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isDetailedBillingSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    class="sidebar-w-750"
    [disableClose]="true">
    @if (isDetailedBillingSideNavOpen) {
      <app-billing-sidenav
        [billingDetails]="selectedDetailedBillingData"
        (closeSideNav)="toggleDetailedBillingSideNav(false, null)">
      </app-billing-sidenav>
    }
  </mat-sidenav>
</mat-sidenav-container>

<div class="auth-page-with-header">
  <div class="filter-and-count-wrapper">
    <div class="search-and-count-wrapper-auth">
      <div class="search-and-count-wrapper">
        <div class="total-users">
          Total: <span class="border-0">{{ totalCount }}</span>
        </div>
      </div>
    </div>

    <div class="filter-wrapper">
      <mat-form-field class="search-bar-wrapper me-2" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
        <mat-select [(ngModel)]="selectedUserId">
          <div class="search-bar mx-2">
            <mat-form-field class="search-bar-wrapper search-bar-wrapper-instructor">
              <input matInput placeholder="Search.." [(ngModel)]="searchTerm" (keydown.space)="$event.stopPropagation()" />
              <mat-icon matTextPrefix>search</mat-icon>
            </mat-form-field>
          </div>
          <div class="mat-option-wrapper">
            <mat-option [value]="all.ALL" (click)="setStudentDetail(null)">All Students</mat-option>
            <mat-option *ngFor="let student of studentList | filter : searchTerm" [value]="student.id" (click)="setStudentDetail(student)">
              {{ student.name }}
            </mat-option>
          </div>
        </mat-select>
      </mat-form-field>
  
      <mat-form-field>
        <mat-date-range-input [rangePicker]="picker">
          <input matStartDate placeholder="Start date" [(ngModel)]="filters.startDateFilter" (click)="picker.open()" />
          <input matEndDate placeholder="End date" [(ngModel)]="filters.endDateFilter"
            (dateChange)="getBillHistoryDetails()" (click)="picker.open()" />
        </mat-date-range-input>
        <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
        <mat-date-range-picker #picker></mat-date-range-picker>
      </mat-form-field>
    </div>
  </div>

  <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : billingLists"></ng-container>
</div>

<ng-template #billingLists>
  <ng-container [ngTemplateOutlet]="billHistoryDetails && billHistoryDetails.length ? openBillList : noDataFound"></ng-container>
</ng-template>

<ng-template #openBillList>
  <div class="visits-list">
    @for (billHistory of billHistoryDetails; track $index) {
    <div class="o-card mb-2 pointer">
      <div class="o-card-body"
        [ngClass]="{ 'pointer': currentUser?.userRoleId === constants.roleIds.ADMIN || currentUser?.userRoleId === constants.roleIds.DESK_MANAGER }">
        <div (click)="toggleDetailedBillingSideNav(true, billHistory.userBillingTransactions)">
          <div>
            <div class="title" *appHasPermission="[constants.roles.USER]">
              <ng-container [ngTemplateOutlet]="scheduleName" [ngTemplateOutletContext]="{ billHistory: billHistory }"></ng-container>
            </div>
            <div class="title admin-title" matTooltip="Account Manager"
              *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
               {{ billHistory.userBillingTransactions.accountManagerName | titlecase }}
            </div>
          </div>

          <div class="visit-content mb-2" *appHasPermission="[constants.roles.USER]">
            <div class="visit-info">
              <div class="me-1">Transaction Id:</div>
              <div class="text-black">#{{ billHistory.userBillingTransactions.transactionId }}</div>
            </div>
          </div>
          <div class="visit-content mb-2" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
            <div class="visit-info">
              <div class="me-1">Email</div>
              <div class="text-black">{{ billHistory.userBillingTransactions.accountManagerEmail }}</div>
            </div>
            <div class="dot"></div>
            <div class="visit-info">
              <div class="me-1">Phone No.</div>
              <div class="text-black">{{ billHistory.userBillingTransactions.accountManagerPhone }}</div>
            </div>
            <div class="dot"></div>
            <div class="visit-info">
              <div class="me-1">Transaction Id:</div>
              <div class="text-black">#{{ billHistory.userBillingTransactions.transactionId }}</div>
            </div>
          </div>

          <div class="visit-content">
            <div class="visit-info">
              <div class="me-1">Payment Date:</div>
              <div class="primary-color">
                {{ billHistory.userBillingTransactions.billPaymentDate | date: 'mediumDate' }}
              </div>
            </div>
            <div class="dot"></div>
            <div class="visit-info">
              <div class="me-1 text-black">Dependent</div>
              <div class="primary-color">
                @if (billHistory.userBillingTransactions.dependentName) {
                  {{ billHistory.userBillingTransactions.dependentName | titlecase }}
                }
                @else {
                  {{ getDependentNames(billHistory.userBillingTransactions)[0] }}
                  @if (getDependentNames(billHistory.userBillingTransactions).length > 1) {
                    <div class="dot"></div>
                    <span [matTooltip]="getDependentNames(billHistory.userBillingTransactions).join(', ')"> +{{ getDependentNames(billHistory.userBillingTransactions).length - 1 }}</span>
                  }
                }
              </div>
            </div>
            <div class="dot" *appHasPermission="[constants.roles.USER]"></div>
            <div class="visit-info" *appHasPermission="[constants.roles.USER]">
              <div class="primary-color me-1">
                {{
                schedulerService.getClassType(billHistory.userBillingTransactions.classType)
                }}
              </div>
              <div>Lesson</div>
            </div>
          </div>
        </div>

        <div class="visit-cancel-info">
          @if (billHistory.userBillingTransactions && billHistory.userBillingTransactions.discountedAmount) {
          <div [matTooltip]="'Discounted Amount: $' + billHistory.userBillingTransactions.discountedAmount">
            {{
            billHistory.userBillingTransactions.billAmount | currency: 'USD' : 'symbol' : '1.2-2'
            }}
          </div>
          }
          @else {
          <div>
            {{
            billHistory.userBillingTransactions.billAmount | currency: 'USD' : 'symbol' : '1.2-2'
            }}
          </div>
          }
          <div *ngIf="!billHistory.userBillingTransactions.isRefunded">
            <button
              *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]"
              mat-raised-button
              color="primary"
              class="mat-primary-btn mt-2"
              type="button"
              (click)="onRefundConfirmation(billHistory.userBillingTransactions)"
              [appLoader]="showBtnLoader">
              Refund
            </button>
          </div>
          <div *ngIf="billHistory.userBillingTransactions.isRefunded" class="schedule-status">
              <img [src]="constants.staticImages.icons.checkCircle" class="present-img" alt="" /> Refunded
          </div>
        </div>
      </div>
    </div>
    }
  </div>
</ng-template>

<ng-template #scheduleName let-billHistory="billHistory">
  @switch (billHistory.userBillingTransactions.classType) {
  @case (classType.INTRODUCTORY) {
  <div>
    Introductory {{ billHistory.userBillingTransactions.instrumentName | titlecase }} Lesson
  </div>
  }
  @case (classType.RECURRING) {
  <div>
    @if (billHistory.userBillingTransactions.planId) {
      @switch (true) {
        @case (billHistory.userBillingTransactions.isDDDPlan) {
          DDD Plan ({{ billHistory.userBillingTransactions.planDetails[0].planDetail.duration }})
        }
        @case (billHistory.userBillingTransactions.isRentalPlan) {
          {{ billHistory.userBillingTransactions.instrumentName | titlecase }} Rental Plan {{ billHistory.userBillingTransactions.isPlanWithInsurance ? 'With Insurance' : 'Without Insurance' }}
        }
        @default {
          Weekly {{ getCurrentPlanDetail(billHistory.userBillingTransactions)?.planInstrument | titlecase }} Lessons
          ({{
          planSummaryService.getPlanType(getCurrentPlanDetail(billHistory.userBillingTransactions)!.planType)
          }} -
          {{
          planSummaryService.getPlanSummary(getCurrentPlanDetail(billHistory.userBillingTransactions)!.planDetails)
          }})
        }
      }
    }
    @else {
      {{ billHistory.userBillingTransactions.billPaymentDate | date: constants.dateFormats.month }} Recurring Lesson Bill
    }
  </div>
  }
  @case (classType.GROUP_CLASS) {
  <div>
    {{ billHistory.userBillingTransactions.groupClassName | titlecase }}
  </div>
  }
  @case (classType.SUMMER_CAMP) {
  <div>
    {{ billHistory.userBillingTransactions.summerCampName | titlecase }}
  </div>
  }
  @case (classType.ENSEMBLE_CLASS) {
  <div>
    {{ billHistory.userBillingTransactions.ensembleClassName | titlecase }}
  </div>
  }
  }
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-card">
    <h3>NO DATA FOUND!</h3>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>