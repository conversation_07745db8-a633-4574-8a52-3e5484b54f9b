import { ChangeDetectorRef, Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { SharedModule } from 'src/app/shared/shared.module';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { CommonModule } from '@angular/common';
import {
  BlockLocationFormGroup,
  BlockLocationParams,
  SchoolLocations,
  SchoolLocationDayOff
} from 'src/app/pages/room-and-location-management/models';
import { MatButtonModule } from '@angular/material/button';
import { LocationService } from '../../services';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MultiSelectChipsComponent } from 'src/app/shared/components/multi-select-chips/multi-select-chips.component';
import { CBGetResponse, CBResponse, IdNameModel } from 'src/app/shared/models';
import { DateUtils } from 'src/app/shared/utils/date.utils';
import { MatTooltipModule } from '@angular/material/tooltip';

const DEPENDENCIES = {
  MODULES: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule,
    SharedModule,
    MatDatepickerModule,
    MatTooltipModule
  ],
  COMPONENTS: [MultiSelectChipsComponent]
};

@Component({
  selector: 'app-block-location',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  providers: [provideNativeDateAdapter()],
  templateUrl: './block-location.component.html',
  styleUrl: './block-location.component.scss'
})
export class BlockLocationComponent extends BaseComponent implements OnInit {
  blockLocationFormGroup!: FormGroup<BlockLocationFormGroup>;
  locationDayOffList: Array<SchoolLocationDayOff> = [];
  selectedBlockLocationDetails: SchoolLocationDayOff | null = null;
  maxDate = new Date();
  blockLocationParams: BlockLocationParams = {
    locationId: {
      id: 1,
      defaultPlaceholder: 'Select Location(s)',
      placeholder: 'Select Location(s)',
      value: [] as Array<IdNameModel>,
      totalCount: 0,
      isOpen: false,
      showSearchBar: true,
      showMax: 1,
      options: [] as Array<IdNameModel>
    }
  };

  @Output() closeSideNav = new EventEmitter<void>();

  constructor(
    private readonly locationService: LocationService,
    private readonly commonService: CommonService,
    private readonly toasterService: AppToasterService,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.initBlockLocationForm();
    this.getAllLocations();
    this.getAllLocationDayOff();
  }

  initBlockLocationForm(): void {
    this.blockLocationFormGroup = new FormGroup<BlockLocationFormGroup>({
      dayOffStartDate: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      dayOffEndDate: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      reason: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
      locationIds: new FormControl([], { nonNullable: true }),
      id: new FormControl(undefined, { nonNullable: true })
    });
  }

  getAllLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.blockLocationParams.locationId.options = res.result.items.map(location => ({
            id: location.schoolLocations.id,
            name: location.schoolLocations.locationName
          }));
          this.cdr.detectChanges();
        }
      });
  }

  getAllLocationDayOff(): void {
    this.showPageLoader = true;
    this.locationService
      .getList<CBGetResponse<SchoolLocationDayOff[]>>(API_URL.schoolLocations.getAllSchoolLocationDayOff)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<SchoolLocationDayOff[]>) => {
          this.locationDayOffList = res.result.map(item => ({
            ...item,
            locationNames: this.getLocationNamesFromIds(item.locationIds),
            dayOffStartDate: DateUtils.toLocal(item.dayOffStartDate),
            dayOffEndDate: DateUtils.toLocal(item.dayOffEndDate)
          }));
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  selectedLocationIds(): void {
    this.blockLocationFormGroup.controls.locationIds.setValue(this.blockLocationParams.locationId.value.map(item => item.id));
  }

  getLocationNamesFromIds(locationIds: number[]): string {
    const locationArray = [];
    for (const id of locationIds) {
      const locationName = this.blockLocationParams.locationId.options.find(item => item.id === id)?.name;
      if (locationName) {
        locationArray.push(locationName);
      }
    }
    return locationArray.join(', ');
  }

  onEditLocationDayOff(dayOff: SchoolLocationDayOff): void {
    this.selectedBlockLocationDetails = dayOff;
    this.blockLocationFormGroup.patchValue({ ...dayOff });

    const selectedLocations = dayOff.locationIds
      .map(id => {
        const location = this.blockLocationParams.locationId.options.find(option => option.id === id);
        return location ? { id: location.id, name: location.name } : null;
      })
      .filter(location => location !== null) as Array<IdNameModel>;

    this.blockLocationParams.locationId.value = selectedLocations;
    this.cdr.detectChanges();
  }

  onSubmit(): void {
    if (this.blockLocationFormGroup.invalid) {
      this.blockLocationFormGroup.markAllAsTouched();
      return;
    }
    this.blockLocationFormGroup.markAsUntouched();
    this.showBtnLoader = true;
    this.locationService
      .add(
        {
          ...this.blockLocationFormGroup.getRawValue(),
          dayOffStartDate: DateUtils.getUtcRangeForLocalDate(this.blockLocationFormGroup.getRawValue().dayOffStartDate).startUtc,
          dayOffEndDate: DateUtils.getUtcRangeForLocalDate(this.blockLocationFormGroup.getRawValue().dayOffEndDate).endUtc
        },
        API_URL.schoolLocations.createOrEditSchoolLocationDayOff
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.showBtnLoader = false;
          this.selectedBlockLocationDetails
            ? this.toasterService.success(this.constants.successMessages.updatedSuccessfully.replace('{item}', 'Location Day Off'))
            : this.toasterService.success(this.constants.successMessages.savedSuccessfully.replace('{item}', 'Location Day Off'));
          this.resetData();
          this.getAllLocationDayOff();
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  resetData(): void {
    this.selectedBlockLocationDetails = null;
    this.blockLocationParams.locationId.value = [];
    this.blockLocationFormGroup.reset();
    this.cdr.detectChanges();
  }

  closeSideNavFun(): void {
    this.resetData();
    this.closeSideNav.emit();
  }
}
