<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isDocumentSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    [ngClass]="showPaymentTemplate ? 'sidebar-w-850' : 'xl-sidebar'"
    [disableClose]="true">
    @if (isDocumentSideNavOpen) {
      <app-pdf-viewer
        [documentInfo]="documentInfo"
        [isPlanRenewal]="documentInfo.isPlanRenewal || false"
        (closeSideNav)="isDocumentSideNavOpen = false; showPaymentTemplate = false"
        (shrinkMatSideNav)="showPaymentTemplate = true"
        (refereshDocuments)="getDocumentDetails()"></app-pdf-viewer>
    }
  </mat-sidenav>

  <mat-sidenav-content>
    <div class="header-tab-with-btn">
      <div class="tab-item-content">
        @for (pageTabOption of pageTabOptions | keyvalue; track $index) {
          <div
            [ngClass]="{ item: true, 'active-item': selectedTabOption === pageTabOption.value }"
            (click)="setActiveTabOption(pageTabOption.value)">
            {{ pageTabOption.value }}
          </div>
        }
      </div>
    </div>

    <div class="auth-page-with-header">
      <div class="search-and-count-wrapper-auth">
        <div class="search-and-count-wrapper">
          <div class="total-users">
            Total:
            <span>{{
              filteredDocumentDetails && filteredDocumentDetails.length
            }}</span>
          </div>
          <div class="search-bar">
            <mat-form-field class="search-bar-wrapper ms-3">
              <input matInput placeholder="Search.." [(ngModel)]="searchTerm" (ngModelChange)="onSearchTermChanged()" />
              <mat-icon matTextPrefix>search</mat-icon>
            </mat-form-field>
          </div>
        </div>
      </div>
      <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : documentLists"></ng-container>
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>

<ng-template #documentLists>
  <ng-container
    [ngTemplateOutlet]="filteredDocumentDetails && filteredDocumentDetails.length ? mainDocList : noDataFound"></ng-container>
</ng-template>

<ng-template #mainDocList>
  <div class="document-list"> 
    @for (signedDocument of filteredDocumentDetails; track $index) {
      <div class="o-card mb-2">
        <div class="o-card-body">
          <div>
            <div class="title">{{ signedDocument.signedDocuments.documentName }}</div>
            <div class="document-content">
              <div>
                Recieved on: {{ signedDocument.signedDocuments.uploadDate | localDate | date }} -
                {{ signedDocument.signedDocuments.uploadDate | localDate | date: "shortTime" }}
              </div>
              <div class="dot"></div>
              <div>
                Status:
                <span
                  [ngClass]="{
                    pending: !(signedDocument.signedDocuments.isAgreementDone && signedDocument.signedDocuments.isPaid) && !signedDocument.signedDocuments.isPlanRenewal  
                  }">
                  @if (signedDocument.signedDocuments.isPlanRenewal) {
                    Re-Enrollment
                  }
                  @else {
                    <ng-container
                      *ngIf="
                        signedDocument.signedDocuments.isAgreementDone && signedDocument.signedDocuments.isPaid;
                        else statusPayment
                      "
                      >Agreed</ng-container
                    >
                  }
                  <ng-template #statusPayment>
                    <ng-container
                      *ngIf="
                        signedDocument.signedDocuments.isAgreementDone && !signedDocument.signedDocuments.isPaid;
                        else statusAcceptTC
                      "
                      >Payment Pending</ng-container
                    >
                  </ng-template>
                  <ng-template #statusAcceptTC>Pending</ng-template>
                </span>
              </div>
              <div class="dot"></div>
              <div class="text-black">
                Sent by <span class="primary-color">{{ signedDocument.signedDocuments.documentSendBy }}</span>
              </div>
            </div>
          </div>
          <div>
            <button
              mat-raised-button
              color="primary"
              class="mat-primary-btn action-btn"
              type="button"
              (click)="openPDFViewer(signedDocument.signedDocuments)">
              <ng-container
                *ngIf="
                  signedDocument.signedDocuments.isAgreementDone && (signedDocument.signedDocuments.isPaid || signedDocument.signedDocuments.isPlanRenewal);
                  else payment
                "
                >View Pdf</ng-container
              >
              <ng-template #payment>
                <ng-container
                  *ngIf="
                    signedDocument.signedDocuments.isAgreementDone && !signedDocument.signedDocuments.isPaid;
                    else acceptTC
                  "
                  >Payment</ng-container
                >
              </ng-template>
              <ng-template #acceptTC>Accept T&C</ng-template>
            </button>
          </div>
        </div>
      </div>
    }
  </div>
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-card">
    <h3>NO DATA FOUND!</h3>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
