import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { AdvanceFiltersForParams, AdvancedFilters, DependentInfoSchedule } from '../../models';
import { CBResponse, IdNameModel } from 'src/app/shared/models';
import { MatIconModule } from '@angular/material/icon';
import { MultiSelectChipsComponent } from 'src/app/shared/components/multi-select-chips/multi-select-chips.component';
import { DependentService } from 'src/app/pages/profile/services';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FormsModule } from '@angular/forms';
import { CommonUtils } from 'src/app/shared/utils';
import { SharedModule } from 'src/app/shared/shared.module';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    CommonModule,
    MatIconModule,
    MultiSelectChipsComponent,
    MatCheckboxModule,
    FormsModule,
    SharedModule
  ]
};

@Component({
  selector: 'app-schedule-advance-filter',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES],
  templateUrl: './schedule-advance-filter.component.html',
  styleUrl: './schedule-advance-filter.component.scss'
})
export class ScheduleAdvanceFilterComponent extends BaseComponent implements OnChanges {
  @Input() appliedAdvanceFilter!: AdvancedFilters;
  @Input() instructorIds!: Array<number>;

  @Output() applyFilters = new EventEmitter<AdvancedFilters>();
  @Output() showActiveStaffOnly = new EventEmitter<boolean>();
  @Output() closeFilterModal = new EventEmitter<void>();

  filterParams: AdvanceFiltersForParams = {
    student: {
      id: 1,
      defaultPlaceholder: 'Select Client',
      placeholder: 'Select Client',
      value: [] as Array<IdNameModel>,
      totalCount: 0,
      showMax: 1,
      isOpen: false,
      showSearchBar: true,
      options: [] as Array<IdNameModel>
    }
  };

  constructor(
    private readonly dependentService: DependentService,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['appliedAdvanceFilter']?.currentValue) {
      this.appliedAdvanceFilter = changes['appliedAdvanceFilter']?.currentValue;
    }
  }

  ngOnInit(): void {
    this.getStudents();
  }

  getFilterParamsForStudent(): void {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      instructorFilter: this.instructorIds,
      page: 1
    });
  }

  getStudents(): void {
    this.dependentService
      .add(this.getFilterParamsForStudent(), API_URL.crud.getAll)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<DependentInfoSchedule>) => {
          const { items, totalCount } = res.result;
          this.filterParams.student.options = items.map((item) => ({
            id: item.dependentInformation.id,
            name: `${item.dependentInformation.firstName} ${item.dependentInformation.lastName}`
          }));
          this.filterParams.student.totalCount = totalCount;
          this.setStudentFilterValues(this.appliedAdvanceFilter);
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  removeAllFilters(): void {
    this.applyFilters.emit({
      studentIdFilter: [],
      skillTypeFilter: null,
      roomIdFilter: [],
      lessonTypeFilter: null,
      studentAgeFilter: null,
      isSpecialNeedsFilter: false,
      showActiveStaffOnly: false
    });
    this.filterParams.student.value = [];
    this.closeFilterDropdowns();
  }

  onApplyFilters(): void {
    const selectedStudentIds = this.filterParams.student.value.map(({ id }) => id);
    this.applyFilters.emit({
      ...this.appliedAdvanceFilter,
      studentIdFilter: selectedStudentIds
    });
    this.closeFilterDropdowns();
  }

  closeModal(): void {
    this.closeFilterModal.emit();
    this.closeFilterDropdowns();
  }

  closeFilterDropdowns(): void {
    this.filterParams.student.isOpen = false;
  }

  setStudentFilterValues(advanceFilter: AdvancedFilters): void {
    if (advanceFilter) {
      this.filterParams.student.value = [];
      const { studentIdFilter } = advanceFilter;
      const { options, value } = this.filterParams.student;
      const existingIds = new Set(value.map((o) => o.id));
      const selectedStudents = studentIdFilter
        .map((studentId) => options.find((o) => o.id === studentId))
        .filter((student) => student && !existingIds.has(student.id)) as IdNameModel[];

      this.filterParams.student.value = [...value, ...selectedStudents];
    }
  }
}
