import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { AccordionType, AssignItemType, DependentInformations } from '../../models';
import { AddSchedule, AssignedPlanStatus, ClassTypes, StudentPlans } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { PlanSummaryService } from 'src/app/pages/settings/pages/plan/services';
import {
  All,
  Duration,
  Plan,
  PlanFilters,
  PlanSummaries,
  PlanSummary,
  PlanType,
  VisitsPerWeek
} from 'src/app/pages/settings/pages/plan/models';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule } from '@angular/forms';
import { EnumToKeyValuePipe, LocalDatePipe } from 'src/app/shared/pipe';
import { CommonUtils } from 'src/app/shared/utils';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CBGetResponse, CBResponse, MatDialogRes } from 'src/app/shared/models';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { Instrument } from 'src/app/request-information/models';
import { DependentService } from 'src/app/pages/profile/services';
import { MatSidenavModule } from '@angular/material/sidenav';
import { BookAssignedPlanComponent } from '../book-assigned-plan/book-assigned-plan.component';
import { StudentPlanService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { AvailableEnsembleListComponent } from '../available-ensemble-list/available-ensemble-list.component';
import { StoreProductService } from 'src/app/pages/shop/services';
import { AssignProductComponent } from '../assign-product/assign-product.component';
import { PurchasedItems, TransactionTypes } from 'src/app/pages/shop/models';
import { ViewShoppingCartComponent } from '../view-shopping-cart/view-shopping-cart.component';
import { CartDetailsComponent } from '../../../../../billing/pages/product-billing/pages/cart-details/cart-details.component';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { PdfViewerComponent } from 'src/app/pages/user-document/pages/pdf-viewer/pdf-viewer.component';
import { SignedDocumentsInfo } from 'src/app/pages/user-document/models';
import { ContinueToCheckoutComponent } from '../continue-to-checkout/continue-to-checkout.component';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    SharedModule,
    CommonModule,
    MatIconModule,
    MatFormFieldModule,
    MatSelectModule,
    FormsModule,
    MatSidenavModule,
    MatTooltipModule
  ],
  PIPES: [EnumToKeyValuePipe, LocalDatePipe],
  COMPONENTS: [
    BookAssignedPlanComponent,
    AvailableEnsembleListComponent,
    AssignProductComponent,
    ViewShoppingCartComponent,
    CartDetailsComponent,
    PdfViewerComponent,
    ContinueToCheckoutComponent
  ]
};

@Component({
  selector: 'app-assign-plan-and-product',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './assign-plan-and-product.component.html',
  styleUrl: './assign-plan-and-product.component.scss'
})
export class AssignPlanAndProductComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() selectedStudentDetails!: DependentInformations | undefined;

  isAssignedPlanAccordionOpen = true;
  isOtherPlanAccordionOpen = true;
  isEnsemblePlanAccordionOpen = true;
  isAssignedProductAccordionOpen = true;
  isRentalPlanAccordionOpen = true;
  isBookAssignedPlanSideNavOpen = false;
  isEnsembleClassAvailable = false;
  isEnsembleClassesSideNavOpen = false;
  isContinueToCheckoutSideNavOpen = false;
  showAssignedProductLoader = false;
  isAssignProductSideNavOpen = false;
  isShoppingCartSideNavOpen = false;
  isViewProductSideNavOpen = false;
  isPDFViewerSideNavOpen = false;
  showAssignedPlanLoader = false;
  showPaymentTemplate = false;

  all = All;
  plansEnum = Plan;
  planTypes = PlanType;
  durations = Duration;
  visits = VisitsPerWeek;
  accordionType = AccordionType;
  activeEnumValue = AssignItemType.PLANS;
  transactionTypes = TransactionTypes;
  assignedPlanStatuses = AssignedPlanStatus;
  assignedItemTypes = AssignItemType;
  ensemblePlans!: Array<PlanSummaries>;
  otherPlans!: Array<PlanSummaries>;
  rentalPlans!: Array<PlanSummaries>;
  instruments!: Array<Instrument>;
  studentPlans!: Array<StudentPlans>;
  studentProducts!: Array<PurchasedItems>;
  selectedStudentPlan!: PlanSummary | undefined;
  selectedInstrumentName!: string;
  documentInfo!: SignedDocumentsInfo;
  selectedProduct!: PurchasedItems | null;
  bookPlanFormValue!: AddSchedule;
  totalItemsInCart!: number;
  filters: PlanFilters = {
    planFilter: 1,
    planTypeFilter: 0,
    DurationFilter: 0,
    VisitsPerWeekFilter: 0
  };

  @Output() closeSideNav = new EventEmitter<void>();

  constructor(
    protected readonly planSummaryService: PlanSummaryService,
    private readonly cdr: ChangeDetectorRef,
    private readonly commonService: CommonService,
    private readonly toasterService: AppToasterService,
    private readonly dependentService: DependentService,
    private readonly studentPlanService: StudentPlanService,
    private readonly storeProductService: StoreProductService,
    private readonly dialog: MatDialog
  ) {
    super();
  }

  ngOnInit(): void {
    this.getPlanDetail();
    this.getInstruments();
    this.getShoppingCart();
  }

  ngOnChanges(): void {
    this.getStudentPlans(this.selectedStudentDetails?.id!);
    this.getStudentProducts();
  }

  getInstruments(): void {
    this.commonService
      .getInstruments()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          this.instruments = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getFilterParams() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      planFilter: this.filters.planFilter,
      planTypeFilter: this.filters.planTypeFilter,
      DurationFilter: this.filters.DurationFilter,
      VisitsPerWeekFilter: this.filters.VisitsPerWeekFilter
    });
  }

  getPlanDetail(): void {
    this.showPageLoader = true;
    this.planSummaryService
      .getListWithFilters<CBResponse<PlanSummaries>>(this.getFilterParams(), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<PlanSummaries>) => {
          const allPlans = res.result.items.map(plan => ({
            ...plan,
            planSummary: {
              ...plan.planSummary,
              instrumentId: 0
            }
          }));
          this.ensemblePlans = allPlans.filter(plan => plan.planSummary.isEnsembleAvailable);
          this.otherPlans = allPlans.filter(plan => !plan.planSummary.isEnsembleAvailable && !plan.planSummary.isRentalPlan);
          this.rentalPlans = allPlans.filter(plan => plan.planSummary.isRentalPlan);

          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getStudentPlans(studentId: number): void {
    this.showAssignedPlanLoader = true;
    this.studentPlanService
      .getList<CBResponse<StudentPlans>>(`${API_URL.studentPlans.getStudentPlans}?DependentInformationId=${studentId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<StudentPlans>) => {
          this.studentPlans = res.result.items;
          this.isEnsembleClassAvailable = this.studentPlans.some(plan => plan.isEnsembleAvailable);
          this.showAssignedPlanLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showAssignedPlanLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getInstrumentName(id: number): void {
    this.selectedInstrumentName = this.instruments?.find(name => name.instrumentDetail.id === id)?.instrumentDetail.name ?? '';
  }

  getCustomerOrderParams(getPastOrder: boolean) {
    return {
      StudentId: this.selectedStudentDetails?.id,
      Page: 1,
      GetPastOrder: getPastOrder,
      UserId: this.selectedStudentDetails?.accountManagerId,
      LocationId: this.selectedStudentDetails?.locationId
    };
  }

  getStudentProducts(): void {
    this.showAssignedProductLoader = true;
    this.storeProductService
      .getListWithFilters<CBResponse<PurchasedItems>>(this.getCustomerOrderParams(true), API_URL.storeProduct.getCustomerOrders)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<PurchasedItems>) => {
          this.studentProducts = res.result.items;
          this.showAssignedProductLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showAssignedProductLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getShoppingCart(): void {
    this.storeProductService
      .getListWithFilters<CBGetResponse<PurchasedItems>>(this.getCustomerOrderParams(false), API_URL.storeProduct.getProductCartView)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<PurchasedItems>) => {
          this.totalItemsInCart = res.result.cartItems?.length || 0;
          this.cdr.detectChanges();
        }
      });
  }

  toggleAccordion(accordionType: AccordionType): void {
    switch (accordionType) {
      case AccordionType.ASSIGNED_PLAN:
        this.isAssignedPlanAccordionOpen = !this.isAssignedPlanAccordionOpen;
        break;
      case AccordionType.ENSEMBLE_PLAN:
        this.isEnsemblePlanAccordionOpen = !this.isEnsemblePlanAccordionOpen;
        break;
      case AccordionType.OTHER_PLAN:
        this.isOtherPlanAccordionOpen = !this.isOtherPlanAccordionOpen;
        break;
      case AccordionType.ASSIGNED_PRODUCT:
        this.isAssignedProductAccordionOpen = !this.isAssignedProductAccordionOpen;
        break;
    }
  }

  toggleCartItem(isOpen: boolean, product: PurchasedItems | null): void {
    this.isViewProductSideNavOpen = isOpen;
    this.selectedProduct = product;
  }

  onSchedule(plan: StudentPlans): void {
    plan.isEnsembleAvailable ? (this.isEnsembleClassesSideNavOpen = true) : (this.isBookAssignedPlanSideNavOpen = true);
    this.selectedStudentPlan = {
      ...plan,
      plandetails: { items: plan.planDetails },
      categoryId: 0,
      categoryName: '',
      id: plan.studentplan.id!,
      fullFilePath: '',
      instrumentId: plan.isEnsembleAvailable ? null : plan.studentplan.instrumentDetailId!,
      isRecurringDiscount: plan.isRecurringDiscount,
      discountedAmount: plan.discountedAmount,
      updatedPlanId: null,
      isDDDPlan: plan.isDDDPlan
    };
  }

  onPayment(plan: StudentPlans): void {
    this.isPDFViewerSideNavOpen = true;
    this.documentInfo = {
      ...plan,
      ...this.selectedStudentDetails,
      ...this.selectedStudentPlan,
      planStartDate: plan.startDate ?? '',
      planAmount: plan.planPrice ?? 0,
      id: plan.agreementDocumentId ?? 0,
      accountManagerId: this.selectedStudentDetails?.accountManagerId ?? 0,
      documentType: 0,
      documentSendBy: '',
      uploadLocalDate: '',
      amount: plan.planPrice ?? 0,
      planId: plan.studentplan.id ?? 0,
      planInstrument: plan.instrumentName ?? '',
      totalAmount: plan.planPrice ?? 0,
      otherDependentPlans: [],
      studentId: this.selectedStudentDetails?.id ?? 0,
      isPlanRenewal: false,
      classType: plan.isEnsembleAvailable ? ClassTypes.ENSEMBLE_CLASS : ClassTypes.RECURRING,
      isClientRequest: false
    };
  }

  openCheckoutSideNav(plan: PlanSummary): void {
    if (!plan.instrumentId && !plan.isEnsembleAvailable) {
      this.toasterService.error(this.constants.errorMessages.selectionPending.replace('{item}', 'Instrument'));
      return;
    }

    this.bookPlanFormValue = {
      ...this.bookPlanFormValue,
      scheduleDate: new Date().toDateString(),
      daysOfSchedule: this.constants.daysOfTheWeek.map(day => day.value)
    };
    this.selectedStudentPlan = plan;
    this.isContinueToCheckoutSideNavOpen = true;
    this.selectedInstrumentName = plan.instrumentName ?? '';
  }

  onAssignPlan(plan: PlanSummary): void {
    if (!plan.instrumentId && !plan.isEnsembleAvailable) {
      this.toasterService.error(this.constants.errorMessages.selectionPending.replace('{item}', 'Instrument'));
      return;
    }

    this.dependentService
      .add(
        {
          planSummaryId: plan.id,
          dependentInformationId: this.selectedStudentDetails?.id,
          instrumentDetailId: plan.isEnsembleAvailable ? null : plan.instrumentId,
          isEnsembleAvailable: plan.isEnsembleAvailable
        },
        API_URL.dependentInformations.assignStudentPlan
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<{ studentPlanId: number }>) => {
          this.selectedStudentPlan = { ...plan, id: res.result.studentPlanId };
          this.selectedInstrumentName = plan.instrumentName ?? '';
          switch (true) {
            case plan.isEnsembleAvailable:
              this.isEnsembleClassesSideNavOpen = true;
              this.bookPlanFormValue = {
                ...this.bookPlanFormValue,
                scheduleDate: new Date().toDateString()
              };
              break;
            default:
              this.isBookAssignedPlanSideNavOpen = true;
              break;
          }

          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  openCancelPlanRequest(planId: number): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Cancel Plan',
        message: `Are you sure you want to cancel this plan?`,
        showReason: true,
        showLastLessonDate: true
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.onCancelPlan(planId, result.reason, result.lastLessonDate);
      }
    });
  }

  onCancelPlan(planId: number, reason?: string, lastLessonDate?: Date): void {
    const reasonAdded = reason ? `&reason=${reason}` : '';
    const lastLessonDateAdded = lastLessonDate ? `&planLastDate=${lastLessonDate}` : '';
    this.studentPlanService
      .add({}, `${API_URL.studentPlans.cancelStudentAssignedPlan}?studentPlanId=${planId}${reasonAdded}${lastLessonDateAdded}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.constants.successMessages.canceledSuccessfully.replace('{item}', 'Plan'));
          this.getStudentPlans(this.selectedStudentDetails?.id!);
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  updateAssignedPlan(): void {
    this.isBookAssignedPlanSideNavOpen = false;
    this.isEnsembleClassesSideNavOpen = false;
    this.getStudentPlans(this.selectedStudentDetails?.id!);
  }

  closeAll(): void {
    this.isBookAssignedPlanSideNavOpen = false;
    this.isEnsembleClassesSideNavOpen = false;
    this.isAssignProductSideNavOpen = false;
    this.isPDFViewerSideNavOpen = false;
    this.closeAssignPlanAndProduct();
  }

  closeAssignPlanAndProduct(): void {
    this.closeSideNav.emit();
  }
}
