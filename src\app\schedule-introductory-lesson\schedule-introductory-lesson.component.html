<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isInstructorsSideNavOpen"
    mode="over"
    position="end"
    #instructorsSideNav
    class="lg-sidebar"
    [disableClose]="true">
    <app-instructor-list
      (resetInstructorSideNav)="resetInstructorSideNav()"
      (closeInstructorSidNav)="toggleInstructorSideNav($event)"
      [selectedInstructorsIdFromParent]="selectedInstructorsId"
      (setSelectedInstructorId)="setSelectedInstructorId($event)"></app-instructor-list>
  </mat-sidenav>

  <mat-sidenav-content>
    <div class="sil-main-wrapper">
      <div class="header-wrapper">
        <img [src]="constants.applicationLogoDarkUrl" alt="" class="octopus-logo" />
        <img [src]="constants.staticImages.icons.gear" alt="" class="gear-logo" />
      </div>

      <div class="schedule-introductory-lesson-content-wrapper">
        <div class="row">
          <div class="col-md-4 col-sm-12 content-detail-wrapper">
            @if (isBasicInfoFilled) {
              <app-details-on-schedule-appointment-page
                [slotOrStaffDetails]="slotOrStaffDetails"
                (goBackToBasicProfile)="setBasicInfoFilled(false)"></app-details-on-schedule-appointment-page>
            } @else {
              <app-schedule-lesson-basic-info [isFromLaunchPage]="isFromLaunchPage"></app-schedule-lesson-basic-info>
            }
          </div>
          <div class="col-md-8 col-sm-12">
            <div class="schedule-info-form-wrapper">
              <div class="form-header-wrapper">
                <div>
                  <div class="title">Take the first step!</div>
                  <div class="sub-title">Learn to play the right way.</div>
                </div>
                <div class="btn-wrapper">
                  @if (!isBasicInfoFilled) {
                    @if (specialNeed) {
                      <button
                        mat-raised-button
                        color="primary"
                        class="mat-primary-btn"
                        type="button"
                        [appLoader]="showBtnLoader"
                        (click)="submitSpecialNeedInfo()">
                        Submit
                      </button>
                    } @else {
                      <button
                        mat-raised-button
                        color="primary"
                        class="mat-primary-btn"
                        type="button"
                        (click)="getSchedulerInfoFormValue()">
                        Continue
                      </button>
                    }
                  } @else {
                    <button
                      mat-raised-button
                      color="accent"
                      class="mat-accent-btn back-btn"
                      (click)="setBasicInfoFilled(false)"
                      type="button">
                      Back
                    </button>
                    <button mat-raised-button color="primary" class="mat-primary-btn" type="button" (click)="redirectToSignIn()">
                      Book Appointment
                    </button>
                  }
                </div>
              </div>
              <div class="divider mt-2"></div>
              <div class="schedule-page-form-content">
                @if (isBasicInfoFilled) {
                  <app-available-appointment-list
                    (scheduleAppointmentsDetails)="setScheduleAppointmentsDetails($event)"
                    (toggleInstructorSideNav)="toggleInstructorSideNav($event)"
                    [scheduleInfo]="scheduleInfo"
                    [selectedInstructorsCount]="
                      selectedInstructorsId.length === totalInstructorsCount ? 'All' : selectedInstructorsId.length
                    "></app-available-appointment-list>
                } @else {
                  <app-scheduler-info-form
                    [specialNeed]="specialNeed"
                    (setSpecialNeedValue)="specialNeed = $event"
                    [scheduleInfo]="scheduleInfo"
                    (showLoaderForRequestInfoBtn)="showBtnLoader = $event"></app-scheduler-info-form>
                }
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>
