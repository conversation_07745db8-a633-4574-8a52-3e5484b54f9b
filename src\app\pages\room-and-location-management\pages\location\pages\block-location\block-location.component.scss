@import "src/assets/scss/variables";
@import "src/assets/scss/theme/_mixins.scss";
 
.field-wrapper {
  @include flex-content-align-center;
  margin-bottom: 20px;
 
  label {
    color: $black-shade-text !important;
    font-size: 16px !important;
    font-weight: 600;
    min-width: 200px;
    margin-bottom: 20px;
  }
 
  .field-content {
    @include flex-content-align-center;
    width: 100%;
 
    .dash {
      margin: 0px 3px;
    }
 
    .dot {
      display: block;
    }
  }
 
  .mat-error-position {
    position: relative;
  }
}
 
.field-with-mat-inputs {
  margin-bottom: 6px;
}
 
::ng-deep {
  .mat-select-custom {
    .mat-mdc-text-field-wrapper {
      height: 35px;
    }

    .mat-mdc-form-field-infix {
      padding-top: 5px !important;
      padding-bottom: 5px !important;
    }

    .mat-mdc-select-placeholder,
    .mat-mdc-select-value-text {
      font-size: 14px !important;
    }

    .multi-select-chips-wrapper {
      padding: 5px 15px !important;

      .select-placeholder {
        color: $dark-gray-text !important;
        font-weight: normal !important;
      }
    }
  }

  .select-box-options-wrapper {
    .mdc-label {
      color: $black-shade-text !important;
    }
  }

  .mat-mdc-form-field-icon-suffix > .mat-icon {
    padding: 0px 5px 16px 0px !important;
  }

  .mat-drawer-inner-container {
    overflow: hidden !important;
  }

  .o-sidebar-wrapper .o-sidebar-body {
    overflow: auto;
    height: calc(100vh - 68px);
    padding: 20px 30px 10px 30px !important;
  }

  .sidenav-content-without-footer {
    overflow: hidden !important;
  }

  .mat-mdc-form-field-error-wrapper {
    padding: 0 !important;
  }

  .mat-start-date {
    .mat-mdc-text-field-wrapper,
    .mat-mdc-icon-button .mat-mdc-button-touch-target,
    .mat-mdc-button-ripple {
      height: 35px;
    }

    .mat-mdc-icon-button.mat-mdc-button-base {
      height: 35px;
      width: 35px;
    }

    .mat-mdc-icon-button .mat-mdc-button-ripple {
      height: 40px;
    }

    .mat-mdc-form-field-infix {
      padding-top: 5px !important;
      padding-bottom: 5px !important;
    }

    .mat-mdc-select-placeholder,
    .mat-mdc-select-value-text {
      font-size: 14px !important;
    }

    .mat-mdc-icon-button svg {
      height: 16px;
    }

    .mat-mdc-icon-button.mat-mdc-button-base {
      padding: 5px;
    }

    .mat-mdc-icon-button.mat-mdc-button-base {
      top: -9px;
    }

    .mat-datepicker-input {
      font-size: 14px !important;
    }

    .mat-mdc-icon-button .mat-mdc-button-persistent-ripple {
      border-radius: 9%;
    }

    .mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before {
      background: transparent !important;
    }
  }

  .mdc-list-item__primary-text {
    @include w-h-100;
  }
}

.location-day-off-section {
  margin-top: 20px;

  .section-title {
    margin-bottom: 20px;

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: $black-shade-text;
      margin: 0;
    }
  }

  .location-day-off-list {
    .location-day-off-card {
      position: relative;
      background-color: $gray-bg-dark;
      border-radius: 8px;
      padding: 20px !important;

      .o-card-body {
        display: flex;
      }

      .card-header {
        @include flex-content-space-between;
        align-items: flex-start;
        margin-bottom: 15px;

        .card-number {
          font-weight: 700;
          color: $primary-color;
          display: inline-block;
          min-width: 30px;
        }
      }

      .edit-icon-wrapper {
        position: absolute;
        right: 15px;
        top: 10px;

        .edit-icon {
          width: 18px;
          height: 18px;
          cursor: pointer;
        }
      }

      .day-off-content {
        .content-row {
          @include flex-content-align-center;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .icon-wrapper {
            @include flex-content-align-center;
            margin-right: 12px;

            img {
              filter: $primary-color-filter;
              height: 18px;
              width: 18px;
            }
          }

          .content-text {
            font-weight: 600;
            word-wrap: break-word;
          }
        }
      }
    }

    .no-data-found-wrapper {
      text-align: center;
      padding: 40px 20px;
      color: $gray-text;

      h3 {
        font-size: 16px;
        font-weight: 500;
        margin: 0;
      }
    }

    .page-loader-wrapper {
      text-align: center;
      padding: 40px 20px;
    }
  }
}

@media (max-width: 767px) {
  .action-btn-wrapper {
    display: flex;
  }

  .field-wrapper,
  .field-content {
    flex-wrap: wrap;

    .dash {
      display: none;
    }
  }
}