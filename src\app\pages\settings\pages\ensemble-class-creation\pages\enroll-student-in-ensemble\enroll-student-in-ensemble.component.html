<div class="o-sidebar-wrapper">
    <div class="o-sidebar-header">
        <div class="title">Enroll Students</div>
        <div class="action-btn-wrapper">
            <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button"
                (click)="onCloseSideNav()">
                Close
            </button>
            <button mat-raised-button color="primary" class="mat-primary-btn" type="button" (click)="onEnrollStudent()"
                [appLoader]="showBtnLoader">
                Save
            </button>
        </div>
    </div>
    <div class="divider"></div>
    <div class="o-sidebar-body">
        <ng-container [ngTemplateOutlet]="scheduleClassDetail"></ng-container>
        <div class="mb-1 fw-bold">Select Clients</div>
        <app-multi-select-chips [filterDetail]="filters.instructor"></app-multi-select-chips>
    </div>
</div>

<ng-template #scheduleClassDetail>
  <div class="group-class-detail-wrapper">
    <div class="schedule-basic-details">
      <div class="schedule-info-header">
        <div class="group-name-age">
            {{ selectedClass?.ensembleClassName | titlecase }} 
        </div>
      </div>

      <div class="schedule-info">
        <img [src]="constants.staticImages.icons.location" alt="" />
        <div class="schedule-info-content">
          {{ selectedClass?.locationName }}
        </div>
      </div>
      <div class="schedule-info" *ngIf="selectedClass?.assignedInstruments?.length">
        <img [src]="constants.staticImages.icons.instrumentIcon" alt="" />
        <div class="schedule-info-content d-flex align-items-center">
            {{ selectedClass?.assignedInstruments![0].instrumentName }}
            @if(selectedClass?.assignedInstruments!.length > 1) {
              <div class="dot d-inline-block"></div>
              <span [matTooltip]="getInstrumentNames(selectedClass?.assignedInstruments!)">+{{ selectedClass?.assignedInstruments!.length - 1 }}</span> 
            }
        </div>
      </div>
      <div class="schedule-info">
        <img [src]="constants.staticImages.icons.profileCircle" alt="" />
        <div class="schedule-info-content d-flex align-items-center">
          <span class="gray-text d-flex align-items-center">With</span>
            {{ selectedClass?.assignedInstructors![0].instructorName }}
            @if(selectedClass?.assignedInstructors!.length > 1) {
              <div class="dot d-inline-block"></div>
              <span [matTooltip]="getInstructorNames(selectedClass?.assignedInstructors!)">+{{ selectedClass?.assignedInstructors!.length - 1 }}</span> 
            }
        </div>
      </div>
      <div class="schedule-info">
        <img [src]="constants.staticImages.icons.calendarIcon" alt="" />
        <div class="schedule-info-content">
          {{ selectedClass?.scheduleStartDate | date: "mediumDate" }}
          <span class="gray-text">(Every {{ schedulerService.getDayOfWeek(selectedClass!.daysOfSchedule) }})</span>
        </div>
      </div>
        <div class="schedule-info">
          <img [src]="constants.staticImages.icons.people" alt="" />
          <div class="schedule-info-content">
            Client Capacity
            <span class="primary-color">{{ selectedClass?.noOfEnrolledStudents ?? all.ALL }}/{{ selectedClass?.studentCapacity }}</span>
          </div>
        </div>
      <div class="schedule-info">
        <img [src]="constants.staticImages.icons.timeCircleClock" alt="" />
          <div class="schedule-info-content">
            {{ selectedClass?.scheduleStartTime | date: "shortTime" }} -
            {{ selectedClass?.scheduleEndTime | date: "shortTime" }}
          </div>
      </div>
    </div>
  </div>
</ng-template>