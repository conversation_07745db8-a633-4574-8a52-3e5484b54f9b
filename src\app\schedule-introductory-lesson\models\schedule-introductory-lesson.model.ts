import { FormControl } from '@angular/forms';
import { InstructorInstrument } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { RequestInformation, RequestInformationFormGroupType } from 'src/app/request-information/models';

export interface SchedulerInfo {
  specialNeed: boolean;
  childAge: number;
  skill: string;
  lessonType: boolean;
  instrumentId: number;
  subInstrumentId: number;
  locationId: number;
  dependentId: number;
}

export interface SchedulerInfoFormGroupType {
  specialNeed: FormControl<boolean>;
  childAge: FormControl<number | undefined>;
  skill: FormControl<string>;
  lessonType: FormControl<boolean | undefined>;
  instrumentId: FormControl<number | undefined>;
  subInstrumentId?: FormControl<number | undefined>;
  locationId: FormControl<number | undefined>;
  dependentId?: FormControl<number | undefined>;
}

export interface SlotOrStaffDetails {
  slotDetails: IntroductoryLesson | undefined;
  showStaffDetails: boolean;
}

export interface Appointments {
  startDate: string;
  introductoryLesson: Array<IntroductoryLesson>;
}

export interface IntroductoryLesson {
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  duration: number;
  lessonName: string;
  lessonInfo: string;
  amount: number;
  instructorId: number;
  ageGroup: number;
  skill: string;
  isVirtualClass: false;
  instrumentId: number;
  instrument: string;
  location: string;
  subInstrumentId: number;
  subInstrument: string;
  locationId: number;
  instructorDetail: Instructor;
  id: number;
  childAge: number;
  lessonType: boolean;
}

export interface Instructor {
  name: string;
  bio: string;
  profilePhoto: string;
  id: number;
  isAvailable: boolean;
  instruments: Array<InstructorInstrument>;
}

export interface InstructorList {
  instructorDetail: Instructor;
}
