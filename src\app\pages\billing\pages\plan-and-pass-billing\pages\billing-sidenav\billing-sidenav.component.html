<div class="o-sidebar-wrapper">
    <div class="o-sidebar-header">
        <div class="title">Billing Details</div>
        <div class="action-btn-wrapper">
            <button mat-raised-button color="accent" class="mat-accent-btn back-btn" type="button"
                (click)="closeSideNavFun()">Close</button>
            <button mat-raised-button color="primary" class="mat-primary-btn" type="button" *ngIf="!billingDetails?.billDiscountedAmount && isOpenBill && currentUser?.userRoleId === constants.roleIds.ADMIN" [appLoader]="showBtnLoader" [disabled]="!discountAmount" (click)="onApplyDiscount()">Apply Discount</button>
        </div>
    </div>
    <div class="divider"></div>
    <div class="o-sidebar-body">
        @if (billingDetails) {
        <div class="plan-details-section" *appHasPermission="[constants.roles.ADMIN, constants.roles.DESK_MANAGER]">
            <div class="plan-details-card">
                <div class="section-header">
                    <i class="material-icons">person</i>
                    <span>Client Details</span>
                </div>
                <div class="plan-detail-row">
                    <div class="plan-detail-label">Account Manager</div>
                    <div class="plan-detail-value">
                        {{ billingDetails.accountManagerName | titlecase }}
                    </div>
                </div>

                <div class="plan-detail-row">
                    <div class="plan-detail-label">Phone No.</div>
                    <div class="plan-detail-value">{{ billingDetails.accountManagerPhone }}</div>
                </div>

                <div class="plan-detail-row">
                    <div class="plan-detail-label">Email address</div>
                    <div class="plan-detail-value">{{ billingDetails.accountManagerEmail }}</div>
                </div>
            </div>
        </div>

        <div class="plan-details-section" *ngIf="!isOpenBill && billingDetails.isRefunded">
            <div class="plan-details-card">
                <div class="section-header">
                    <i class="material-icons">sync_alt</i>
                    <span>Refund Details</span>
                </div>
                <div class="plan-detail-row">
                    <div class="plan-detail-label">Refund Amount</div>
                    <div class="plan-detail-value primary-color">
                        ${{ refundDetail?.refundAmount | number:'1.2-2' }}
                    </div>
                </div>

                <div class="plan-detail-row">
                    <div class="plan-detail-label">Refund Date</div>
                    <div class="plan-detail-value">{{ refundDetail?.refundDate | localDate | date: 'mediumDate' }}</div>
                </div>

                <div class="plan-detail-row">
                    <div class="plan-detail-label">Transaction Id</div>
                    <div class="plan-detail-value">#{{ refundDetail?.transactionId }}</div>
                </div>
            </div>
        </div>

        <div class="plan-details-section" *ngIf="!isOpenBill">
            <div class="plan-details-card">
                <div class="section-header">
                    <i class="material-icons">description</i>
                    <span>Plan Details</span>
                </div>
                <div class="plan-detail-row">
                    <div class="plan-detail-label">Schedule Name</div>
                    <div class="plan-detail-value">
                        <ng-container *ngTemplateOutlet="classTypeTemplate; context: {
                            classType: billingDetails.classType,
                            billingDetails: billingDetails,
                            planDetail: currentPlanDetail,
                            showPlanMeta: false
                        }"></ng-container>
                    </div>
                </div>

                <div class="plan-detail-row">
                    <div class="plan-detail-label">Transaction Id</div>
                    <div class="plan-detail-value">#{{ billingDetails.transactionId }}</div>
                </div>

                <div class="plan-detail-row">
                    <div class="plan-detail-label">Payment Date</div>
                    <div class="plan-detail-value">{{ billingDetails.billPaymentDate | date: 'mediumDate' }}</div>
                </div>
            </div>
        </div>

        @if (otherPlanDetails.length > 0 && billingDetails.planId) {
        <div class="existing-plans-section">
            <div class="existing-plans-list">
                <div class="section-header">
                    <i class="material-icons">list</i>
                    <span>Existing Plan Details</span>
                </div>
                <div class="existing-plan-wrapper">
                    @for (plan of otherPlanDetails; track plan.dependentInformationId) {
                    <div class="existing-plan-item">
                        <div class="plan-number">{{ $index + 1 }}.</div>
                        <div class="plan-info">
                            <div class="plan-name">
                                <ng-container *ngTemplateOutlet="classTypeTemplate; context: {
                                    classType: billingDetails.classType,
                                    billingDetails: billingDetails,
                                    planDetail: plan,
                                    showPlanMeta: true
                                }"></ng-container>
                            </div>
                        </div>
                        <div class="plan-amount">${{ plan.planAmount | number:'1.2-2' }}/Month</div>
                    </div>
                    <div class="dotted-divider" *ngIf="!$last"></div>
                    }
                </div>
            </div>
        </div>
        }

        <div class="plan-details-section">
            <div class="plan-details-card">
                <div class="section-header">
                    <i class="material-icons">account_balance</i>
                    <span>Account Details</span>
                </div>
                <div class="plan-detail-row">
                    <div class="plan-detail-label">Transaction Type</div>
                    <div class="plan-detail-value">{{ transactionTypes[billingDetails.transactionType] | titlecase }}
                    </div>
                </div>

                <div class="plan-detail-row" *ngIf="billingDetails.chequeNumber">
                    <div class="plan-detail-label">Cheque Number</div>
                    <div class="plan-detail-value">{{ billingDetails.chequeNumber }}</div>
                </div>
                @if (billingDetails.transactionType === transactionTypes.ACH) {
                <div class="plan-detail-row">
                    <div class="plan-detail-label">Account Number</div>
                    <div class="plan-detail-value">{{ billingDetails.bankAccountNumber }}</div>
                </div>
                <div class="plan-detail-row">
                    <div class="plan-detail-label">Routing Number</div>
                    <div class="plan-detail-value">{{ billingDetails.bankRoutingNumber }}</div>
                </div>
                }
                @if (billingDetails.transactionType === transactionTypes.CARD) {
                <div class="plan-detail-row">
                    <div class="plan-detail-label">Bank Name</div>
                    <div class="plan-detail-value">{{ billingDetails.ccType }}</div>
                </div>
                <div class="plan-detail-row">
                    <div class="plan-detail-label">Card Number</div>
                    <div class="plan-detail-value">{{ billingDetails.ccNum }}</div>
                </div>
                <div class="plan-detail-row" *ngIf="billingDetails.ccExpiry">
                    <div class="plan-detail-label">Expiry Date</div>
                    <div class="plan-detail-value">{{ billingDetails.ccExpiry.slice(0, 2) + '/' +
                        billingDetails.ccExpiry.slice(2) }}</div>
                </div>
                }
                <div class="plan-detail-row" *ngIf="billingDetails.isRefunded">
                    <div class="plan-detail-label">Reason</div>
                    <div class="plan-detail-value">{{ billingDetails.reason }}</div>
                </div>
            </div>
        </div>

        @if (isOpenBill || (billingDetails.classType === classTypes.RECURRING && !billingDetails.planId)) {
        <div class="plan-details-section">
            <div class="plan-details-card">
                <div class="section-header">
                    <i class="material-icons">payment</i>
                    <span>Payment Details</span>
                </div>
                @for (dependentDetails of billingDetails.dependentBillingDetails; track $index) {
                <div class="plan-detail-row">
                    <div>
                        <span class="plan-detail-label">
                            <ng-container *ngTemplateOutlet="classTypeTemplate; context: {
                                classType: billingDetails.classType,
                                billingDetails: billingDetails,
                                planDetail: dependentDetails,
                                showPlanMeta: true
                            }"></ng-container>
                        </span>
                    </div>
                    <span class="plan-detail-value"> <span *ngIf="!$first">+</span> {{ dependentDetails.planAmount |
                        currency: 'USD' : 'symbol' : '1.2-2' }}</span>
                </div>
                }
                <div class="plan-detail-row" *ngIf="billingDetails.transactionType === transactionTypes.CARD">
                    <span class="plan-detail-label">
                        Card Processing Fee (3.5%)
                    </span>
                    <span class="plan-detail-value">+{{ totalProcessingFee | currency: 'USD' : 'symbol' : '1.2-2'
                        }}</span>
                </div>

                <div class="dotted-divider"></div>

                <div class="plan-detail-row">
                    <span class="plan-detail-label">
                        {{ isOpenBill && billingDetails.billPaymentStatus !== billStatus.FAILED &&
                        currentUser?.userRoleId === constants.roleIds.ADMIN ? 'Bill Amount' : 'Total Payable Amount' }}
                    </span>
                    <span class="plan-detail-value" [ngClass]="{'primary-color': !isOpenBill}">{{  (billingDetails.billDiscountedAmount ? billingDetails.billDiscountedAmount  + billingDetails.billAmount : billingDetails.billAmount) | currency: 'USD' :
                        'symbol' : '1.2-2' }}</span>
                </div>
                @if (isOpenBill && billingDetails.billPaymentStatus !== billStatus.FAILED && currentUser?.userRoleId === constants.roleIds.ADMIN) {
                    <div class="plan-detail-row d-flex align-items-center">
                        @if (billingDetails.billDiscountedAmount) {
                            <span class="plan-detail-label">
                                Discount Applied
                            </span>
                        }
                        @else {
                            <span class="plan-detail-label discount" *ngIf="!showDiscountField" (click)="showDiscountField = true">
                                Apply discount
                            </span>
                        }
                        <span class="plan-detail-label primary-color">
                            @if (showDiscountField) {
                            <div class="space-between discount-row">
                                <div class="discount-input-container">
                                    <mat-form-field>
                                        <mat-label>Discount Amount</mat-label>
                                        <input matInput pattern="[0-9]*\.?[0-9]*" [(ngModel)]="discountAmount"
                                            (input)="capDiscount($event); calculateTotalPrice()"
                                            [max]="billingDetails.billAmount" placeholder="0.00">
                                        <span matPrefix>$&nbsp;</span>
                                        <button mat-icon-button matSuffix (click)="clearDiscount()"
                                            [attr.aria-label]="'Clear discount'" type="button">
                                            <mat-icon>close</mat-icon>
                                        </button>
                                    </mat-form-field>
                                </div>
                            </div>
                            }
                        </span>
                        <div class="plan-detail-value">-${{ discountAmount | number:'1.2-2' }}</div>
                    </div>
                    <div class="dotted-divider"></div>
                    <div class="plan-detail-row">
                        <span class="plan-detail-label">
                            Total Payable Amount
                        </span>
                        <span class="plan-detail-value primary-color">{{( billingDetails.billDiscountedAmount ?  billingDetails.billAmount : billingDetails.billAmount - discountAmount) | currency: 'USD' :
                            'symbol' : '1.2-2' }}</span>
                    </div>
                }
            </div>
        </div>
        }
        @else {
        <div class="plan-details-section">
            <div class="plan-details-card">
                <div class="section-header">
                    <i class="material-icons">payment</i>
                    <span>Payment Details</span>
                </div>
                <div class="plan-detail-row">
                    <div>
                        <span class="plan-detail-label">
                            <ng-container *ngTemplateOutlet="classTypeTemplate; context: {
                                classType: billingDetails.classType,
                                billingDetails: billingDetails,
                                planDetail: currentPlanDetail,
                                showPlanMeta: true
                            }"></ng-container>
                        </span>
                    </div>
                    <span class="plan-detail-value">${{ billingDetails.totalAmount | number:'1.2-2' }}</span>
                </div>

                <div class="plan-detail-row">
                    <span class="plan-detail-label">Discount Applied</span>
                    <span class="plan-detail-value">-${{ billingDetails.discountedAmount | number:'1.2-2' }}</span>
                </div>

                <div class="dotted-divider"></div>

                <div class="plan-detail-row">
                    <span class="plan-detail-label">Paid Amount</span>
                    <span class="plan-detail-value primary-color">${{ billingDetails.paidAmount | number:'1.2-2'
                        }}</span>
                </div>
            </div>
        </div>
        }

        <div class="payment-note mt-3" *ngIf="isDiscountApplied">
            <mat-icon>info</mat-icon>
            <span> <span class="bold">${{ totalDiscount | number:'1.2-2' }}</span> discount is applied to every future
                payment.</span>
        </div>
        }
    </div>
</div>

<ng-template #classTypeTemplate let-classType="classType" let-billingDetails="billingDetails" let-planDetail="planDetail" let-showPlanMeta="showPlanMeta">
    @switch(classType) {
        @case (classTypes.INTRODUCTORY) {
            Introductory {{ billingDetails.instrumentName }} Lesson
        }
        @case (classTypes.RECURRING) {
            @if (planDetail) {
                @switch (true) {
                    @case (planDetail.isDDDPlan) {
                        DDD Plan ({{ planDetail.planDetails[0].planDetail.duration }})
                    }
                    @case (planDetail.isRentalPlan) {
                        {{ planDetail.instrumentName }} Rental Plan {{ planDetail.isPlanWithInsurance ? 'With Insurance' : 'Without Insurance' }}
                    }
                    @default {
                        Weekly {{ planDetail.isEnsembleAvailable ? 'Ensemble' : planDetail.planInstrument }} Lessons ({{
                        planSummaryService.getPlanType(planDetail.planType) }} - {{
                        planSummaryService.getPlanSummary(planDetail.planDetails) }})
                    }
                }
                @if (showPlanMeta) {
                    <div class="plan-meta">
                        <span>For {{ planDetail.dependentName }}</span>
                        <span class="dot"></span>
                        <span>Plan End Date: {{ (planDetail.planEndDate || planDetail.scheduleEndDate) | date:'MMM dd, yyyy' }}</span>
                    </div>
                }
            }
            @else {
                {{ billingDetails.billPaymentDate | date: constants.dateFormats.month }} Recurring Lesson Bill
            }
        }
        @case (classTypes.GROUP_CLASS) {
            {{ billingDetails.groupClassName }}
        }
        @case (classTypes.SUMMER_CAMP) {
            {{ billingDetails.summerCampName }}
        }
        @case (classTypes.ENSEMBLE_CLASS) {
            {{ billingDetails.ensembleClassName }}
        }
    }
</ng-template>