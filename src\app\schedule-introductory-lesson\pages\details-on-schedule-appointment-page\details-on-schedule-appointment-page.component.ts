import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SlotOrStaffDetails } from '../../models';
import { CommonModule } from '@angular/common';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { CommonService } from 'src/app/shared/services';
import { takeUntil } from 'rxjs';
import { CBResponse } from 'src/app/shared/models';
import { Instrument } from 'src/app/request-information/models';
import { SchoolLocations } from 'src/app/pages/room-and-location-management/models';

const DEPENDENCIES = {
  MODULES: [CommonModule]
};

@Component({
  selector: 'app-details-on-schedule-appointment-page',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES],
  templateUrl: './details-on-schedule-appointment-page.component.html',
  styleUrl: './details-on-schedule-appointment-page.component.scss'
})
export class DetailsOnScheduleAppointmentPageComponent extends BaseComponent implements OnChanges {
  @Input() slotOrStaffDetails!: SlotOrStaffDetails | undefined;

  instruments!: Array<Instrument>;
  locations!: Array<SchoolLocations>;

  @Output() goBackToBasicProfile = new EventEmitter<void>();

  constructor(protected readonly schedulerService: SchedulerService, private readonly commonService: CommonService, private readonly cdr: ChangeDetectorRef) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['slotOrStaffDetails']?.currentValue) {
      this.slotOrStaffDetails = changes['slotOrStaffDetails'].currentValue;
    }
  }

  ngOnInit(): void {
    this.getInstruments();
    this.getLocations();
  }

  goBackToBasicProfilePage(): void {
    this.goBackToBasicProfile.emit();
  }

  getInstruments(): void {
    this.commonService
      .getInstruments()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          this.instruments = res.result.items;
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  getLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getInstrumentNameFromValue(value: number | undefined): string | null {
    return this.instruments?.find(name => name.instrumentDetail.id === value)?.instrumentDetail.name || null;
  }

  getLocationNameFromValue(value: number | undefined): string {
    return this.locations?.find(name => name.schoolLocations.id === value)?.schoolLocations.locationName || '';
  }

  showAppointmentDetails(): void {
    if (this.slotOrStaffDetails) {
      this.slotOrStaffDetails.showStaffDetails = false;
    }
  }
}
