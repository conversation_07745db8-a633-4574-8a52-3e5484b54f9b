<mat-form-field class="search-bar-wrapper me-2">
    <input matInput [matDatepicker]="startPicker" (click)="startPicker.open()" [(ngModel)]="selectedYear"
        placeholder="Select Year & Month" (dateChange)="refreshData.emit()" readonly />
    <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
    <mat-datepicker #startPicker startView="multi-year" panelClass="year-only-picker" [startAt]="selectedYear"
        (monthSelected)="chosenMonthHandler($event, startPicker)" (yearSelected)="chosenYearHandler($event)">
    </mat-datepicker>
</mat-form-field>