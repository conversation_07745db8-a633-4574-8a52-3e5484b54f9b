<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isAddNewSideNavOpen || isBlockLocationOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    [ngClass]="isBlockLocationOpen ? 'sidebar-w-750' : 'md-sidebar'"
    [disableClose]="true">
    <ng-container
      [ngTemplateOutlet]="selectedTabOption === pageTabOptions.LOCATION ? addLocation : addRoom"></ng-container>
  </mat-sidenav>

  <mat-sidenav-content>
    <div class="header-tab-with-btn">
      <div class="tab-item-content">
        @for (pageTabOption of pageTabOptions | keyvalue; track $index) {
          <div
            [ngClass]="{ item: true, 'active-item': selectedTabOption === pageTabOption.value }"
            (click)="setActiveTabOption(pageTabOption.value)">
            {{ pageTabOption.value }}
          </div>
        }
      </div>
      <div class="action-btn-wrapper">
        @if (selectedTabOption === pageTabOptions.LOCATION) {
          <button
            mat-raised-button
            *appHasPermission="[constants.roles.ADMIN]"
            color="primary"
            class="mat-primary-btn action-btn"
            type="button"
            (click)="togggleBlockLocation(true)">
            Unavailable Location
          </button>
        }
        <button
          mat-raised-button
          color="primary"
          class="mat-primary-btn action-btn"
          type="button"
          (click)="isAddNewSideNavOpen = true">
          Add {{ selectedTabOption === pageTabOptions.LOCATION ? " Location" : " Room" }}
        </button>
      </div>
    </div>

    <div class="room-and-location-wrapper">
      @if (selectedTabOption === pageTabOptions.LOCATION) {
        <app-location
          (locationDetails)="toggleEditRoomAndLocation($event, true, true)"
          [locationAddedOrEdited]="islocationAdded">
        </app-location>
      } @else {
        <app-room
          (roomView)="toggleView($event, true)"
          (roomInfo)="toggleEditRoomAndLocation($event, true, false)"
          [roomAddedOrEdited]="isRoomUpdated">
        </app-room>
      }
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>

<ng-template #addLocation>
  @if (isBlockLocationOpen) {
    <app-block-location (closeSideNav)="togggleBlockLocation(false)"></app-block-location>
  }
  @else {
    <app-add-new-location
      (closeSideNav)="toggleEditRoomAndLocation(null, false)"
      (locationAddedOrEdited)="islocationAdded = true"
      [selectedLocationDetails]="selectedLocationDetails">
    </app-add-new-location>
  }
</ng-template>

<ng-template #addRoom>
  @if (isRoomView) {
    <app-view-room
      (closeViewSideNav)="toggleView(null, false)"
      (openSideNav)="toggleEditRoomAndLocation($event, true, false)"
      (roomUpdated)="isRoomUpdated = $event"
      [selectedRoomViewDetails]="selectedRoomViewDetails">
    </app-view-room>
  } @else {
    <app-add-new-room
      (openViewSideNav)="toggleView($event, true)"
      (closeSideNav)="toggleEditRoomAndLocation(null, false)"
      (roomAddedOrEdited)="isRoomUpdated = $event"
      [selectedRoomDetails]="selectedRoomDetails"
      [selectedRoomViewDetails]="selectedRoomViewDetails">
    </app-add-new-room>
  }
</ng-template>
