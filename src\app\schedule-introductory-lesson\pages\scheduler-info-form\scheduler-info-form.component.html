<ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : pageContent"></ng-container>

<ng-template #pageContent>
  <div class="schedule-lesson-basic-info-wrapper mt-20">
    <mat-checkbox [(ngModel)]="specialNeed" standalone="true" id="specialNeed" (change)="onCheckboxChange($event)">
      Is this lesson for a client with special needs?
    </mat-checkbox>
    <ng-container [ngTemplateOutlet]="specialNeed ? specialNeedForm : notSpecialNeedForm"></ng-container>
  </div>
</ng-template>

<ng-template #notSpecialNeedForm>
  <form [formGroup]="schedulerInfoForm">
    <div class="field-wrapper mt-20">
      <label for="childAge" class="required">Select Age</label>
      <div>
        <div class="btn-typed-options-wrapper">
          @for (age of ageOptions; track $index) {
            <div
              [ngClass]="{
                'btn-typed-option': true,
                active: schedulerInfoForm.controls.childAge.value === age.value
              }"
              (click)="setFormControlValue('childAge', age.value); getInstruments(age.value); resetValues()">
              {{ age.label }}
            </div>
          }
        </div>
        <mat-error>
          @if (schedulerInfoForm.controls.childAge.touched || schedulerInfoForm.controls.childAge.dirty) {
            <app-error-messages [control]="schedulerInfoForm.controls.childAge"></app-error-messages>
          }
        </mat-error>
      </div>
    </div>

    @if (
      schedulerInfoForm.controls.childAge.value === constants.childAgeValues.nineToSeven ||
      schedulerInfoForm.controls.childAge.value === constants.childAgeValues.eighteenPlus
    ) {
      <div class="field-wrapper">
        <label for="skill" class="required">Select Skill Level</label>
        <div>
          <div class="btn-typed-options-wrapper">
            @for (skill of skillOptions | keyvalue; track $index) {
              <div
                [ngClass]="{
                  'btn-typed-option': true,
                  active: schedulerInfoForm.controls.skill.value === skill.value
                }"
                (click)="setFormControlValue('skill', skill.value)">
                {{ skill.value }}
              </div>
            }
          </div>
          <mat-error>
            @if (schedulerInfoForm.controls.skill.touched || schedulerInfoForm.controls.skill.dirty) {
              <app-error-messages [control]="schedulerInfoForm.controls.skill"></app-error-messages>
            }
          </mat-error>
        </div>
      </div>
    }
    @else {
      <div class="field-wrapper">
        <label for="skill" class="required">Select Skill Level</label>
        <div>
          <div class="btn-typed-options-wrapper">
            <div
              [ngClass]="{
                'btn-typed-option': true,
                active: schedulerInfoForm.controls.skill.value === skillOptions.BEGINNER
              }"
              (click)="setFormControlValue('skill', skillOptions.BEGINNER)">
              Beginner
            </div>
          </div>
          <mat-error>
            @if (schedulerInfoForm.controls.skill.touched || schedulerInfoForm.controls.skill.dirty) {
              <app-error-messages [control]="schedulerInfoForm.controls.skill"></app-error-messages>
            }
          </mat-error>
        </div>
      </div>
    }

    <div class="field-wrapper">
      <label for="lessonType" class="required">Select Lesson Type</label>
      <div>
        <div class="btn-typed-options-wrapper">
          @if (
            !schedulerInfoForm.controls.childAge.value ||
            schedulerInfoForm.controls.childAge.value === 1 ||
            schedulerInfoForm.controls.childAge.value === 2
          ) {
            <div
              [ngClass]="{
                'btn-typed-option': true,
                active: schedulerInfoForm.controls.lessonType.value === false
              }"
              (click)="setFormControlValue('lessonType', false)">
              {{ "In-person Classes" }}
            </div>
          } @else {
            @for (lessonType of lessonTypeOptions; track $index) {
              <div
                [ngClass]="{
                  'btn-typed-option': true,
                  active: schedulerInfoForm.controls.lessonType.value === lessonType.value
                }"
                (click)="setFormControlValue('lessonType', lessonType.value)">
                {{ lessonType.label }}
              </div>
            }
          }
        </div>
        <mat-error>
          @if (schedulerInfoForm.controls.lessonType.touched || schedulerInfoForm.controls.lessonType.dirty) {
            <app-error-messages [control]="schedulerInfoForm.controls.lessonType"></app-error-messages>
          }
        </mat-error>
      </div>
    </div>

    <div class="field-wrapper" *ngIf="schedulerInfoForm.controls.childAge.value">
      <label for="instrumentId" class="required">Select Instrument</label>
      <div>
        <div class="btn-typed-options-wrapper">
          @for (instrumentType of instrumentTypes; track $index) {
            <div
              [ngClass]="{
                'btn-typed-option btn-typed-option-wrap': true,
                active: instrumentType.instrumentDetail.id === schedulerInfoForm.controls.instrumentId.value
              }"
              (click)="setFormControlValue('instrumentId', instrumentType.instrumentDetail.id)">
              {{ instrumentType.instrumentDetail.name }}
            </div>
          }
        </div>
        <mat-error>
          @if (schedulerInfoForm.controls.instrumentId.touched || schedulerInfoForm.controls.instrumentId.dirty) {
            <app-error-messages [control]="schedulerInfoForm.controls.instrumentId"></app-error-messages>
          }
        </mat-error>
      </div>
    </div>

    @if (subInstruments && subInstruments.length) {
      <div class="field-wrapper">
        <label class="required">Select Sub-Instrument</label>
        <div>
          <div class="btn-typed-options-wrapper">
            @for (subInstrument of subInstruments; track $index) {
              <div
                [ngClass]="{
                  'btn-typed-option btn-typed-option-wrap': true,
                  active: subInstrument.subInstrumentDetail.id === schedulerInfoForm.controls.subInstrumentId?.value
                }"
                (click)="setFormControlValue('subInstrumentId', subInstrument.subInstrumentDetail.id)">
                {{ subInstrument.subInstrumentDetail.name }}
              </div>
            }
          </div>
          <mat-error
            *ngIf="
              schedulerInfoForm.controls.subInstrumentId?.touched || schedulerInfoForm.controls.subInstrumentId?.dirty
            ">
            <app-error-messages [control]="schedulerInfoForm.controls.subInstrumentId"></app-error-messages>
          </mat-error>
        </div>
      </div>
    }

    <div class="field-wrapper">
      <label for="locationId" class="required">Select location</label>
      <div class="w-100">
        <mat-form-field>
          <mat-label>Which location do you plan to visit?</mat-label>
          <mat-select formControlName="locationId">
            <mat-option *ngFor="let location of locations" [value]="location.schoolLocations.id">
              {{ location.schoolLocations.locationName }}
            </mat-option>
          </mat-select>
          <mat-error>
            <app-error-messages [control]="schedulerInfoForm.controls.locationId"></app-error-messages>
          </mat-error>
        </mat-form-field>
      </div>
    </div>
  </form>
</ng-template>

<ng-template #specialNeedForm>
  <app-request-information-form
    [isRequestInfoLesson]="false"
    (formSubmitted)="inquiryFormSubmitted($event)"></app-request-information-form>
</ng-template>

<ng-template #showLoader>
  <app-content-loader></app-content-loader>
</ng-template>
