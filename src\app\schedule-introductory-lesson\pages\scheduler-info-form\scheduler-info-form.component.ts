import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SchedulerInfo, SchedulerInfoFormGroupType } from '../../models';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { takeUntil } from 'rxjs';
import { CBResponse } from 'src/app/shared/models';
import { AuthService } from 'src/app/auth/services';
import { SharedModule } from 'src/app/shared/shared.module';
import { RequestInformationFormComponent } from 'src/app/request-information/pages/request-information-form/request-information-form.component';
import { AppToasterService, CommonService, NavigationService } from 'src/app/shared/services';
import { InstrumentsService, SubInstrumentsService } from 'src/app/request-information/services';
import { Instrument, SubInstrument } from 'src/app/request-information/models';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { SchoolLocations } from 'src/app/pages/room-and-location-management/models';

const DEPENDENCIES = {
  MODULES: [
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    ReactiveFormsModule,
    CommonModule,
    MatFormFieldModule,
    SharedModule,
    FormsModule
  ],
  COMPONENTS: [RequestInformationFormComponent]
};

@Component({
  selector: 'app-scheduler-info-form',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './scheduler-info-form.component.html',
  styleUrl: './scheduler-info-form.component.scss'
})
export class SchedulerInfoFormComponent extends BaseComponent implements OnInit {
  @ViewChild(RequestInformationFormComponent) requestInformationFormComponent!: RequestInformationFormComponent;
  @Input() specialNeed!: boolean;
  @Input() scheduleInfo!: SchedulerInfo;

  schedulerInfoForm!: FormGroup<SchedulerInfoFormGroupType>;
  locations!: Array<SchoolLocations>;
  instrumentTypes!: Array<Instrument>;
  subInstruments!: Array<SubInstrument>;

  skillOptions = this.constants.skillOptions;
  ageOptions = this.constants.ageOptions;
  lessonTypeOptions = this.constants.lessonTypeOptions;

  @Output() setSpecialNeedValue = new EventEmitter<boolean>();
  @Output() showLoaderForRequestInfoBtn = new EventEmitter<boolean>();

  constructor(
    private readonly commonService: CommonService,
    private readonly toasterService: AppToasterService,
    private readonly navigationService: NavigationService,
    private readonly instrumentsService: InstrumentsService,
    private readonly subInstrumentService: SubInstrumentsService,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.initSchedulerInfoForm();
    this.getAllLocations();
    this.getInstruments(this.schedulerInfoForm.controls.childAge.value ?? 0);
  }

  getInstruments(ageGroup: number): void {
    if(!ageGroup) return;
    this.instrumentsService
      .getList<CBResponse<Instrument>>(`${API_URL.crud.getAll}?AgeGroup=${ageGroup}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          this.instrumentTypes = res.result.items;
          this.cdr.detectChanges();
        },
        error: () => {
          this.cdr.detectChanges();
        }
      });
  }

  getSubInstruments(): void {
    this.subInstrumentService
      .getList<CBResponse<SubInstrument>>(
        `${API_URL.crud.getAll}?instrumentId=${this.schedulerInfoForm.controls.instrumentId.value}`
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SubInstrument>) => {
          this.subInstruments = res.result.items;
          if (!res.result.items.length) {
            this.schedulerInfoForm.controls.subInstrumentId?.setValue(undefined);
          }
          this.setRequiredBasedOnCondition('subInstrumentId', !!res.result.items.length);
          this.cdr.detectChanges();
        }
      });
  }

  getAllLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  resetValues(): void {
    if (this.scheduleInfo && this.scheduleInfo.instrumentId) {
      this.scheduleInfo.instrumentId = 0;
    }
    this.schedulerInfoForm.controls.instrumentId.reset();
    this.schedulerInfoForm.controls.instrumentId.setValue(undefined);

    this.schedulerInfoForm.controls.lessonType.reset();
    this.schedulerInfoForm.controls.lessonType.setValue(undefined);

    this.schedulerInfoForm.controls.instrumentId.markAsTouched();
    this.schedulerInfoForm.controls.lessonType.markAsTouched();

    this.schedulerInfoForm.controls.instrumentId.updateValueAndValidity();
    this.schedulerInfoForm.controls.lessonType.updateValueAndValidity();
  }

  onCheckboxChange(event: any): void {
    this.setSpecialNeedValue.emit(event.checked);
  }

  setFormControlValue(controlName: string, value: string | number | boolean): void {
    (this.schedulerInfoForm.controls as any)[controlName].setValue(value);
    if (controlName === 'childAge') {
      this.updateSkillsValidator(value as number);
      this.schedulerInfoForm.controls.lessonType.reset();
      this.schedulerInfoForm.controls.lessonType.setValue(undefined);
      this.schedulerInfoForm.controls.lessonType.markAsTouched();
      this.schedulerInfoForm.controls.lessonType.updateValueAndValidity();
    }
  }

  updateSkillsValidator(childAge: number): void {
    const { skill } = this.schedulerInfoForm.controls;
    if (
      childAge === this.constants.childAgeValues.nineToSeven ||
      childAge === this.constants.childAgeValues.eighteenPlus
    ) {
      skill.setValidators([Validators.required]);
    } else {
      skill.clearValidators();
      skill.reset();
    }
    skill.updateValueAndValidity();
  }

  onSubmitSpecialNeedInfo(): void {
    this.requestInformationFormComponent.onSubmit();
    this.showLoaderForRequestInfoBtn.emit(true);
  }

  inquiryFormSubmitted(event: boolean): void {
    if (event) {
      this.toasterService.success(this.constants.successMessages.submittedSuccessfully.replace('{item}', 'Inquiry'));
      this.navigationService.navigateToLogin();
    }
    this.showLoaderForRequestInfoBtn.emit(false);
  }

  initSchedulerInfoForm(): void {
    this.schedulerInfoForm = new FormGroup<SchedulerInfoFormGroupType>({
      specialNeed: new FormControl(false, { nonNullable: true }),
      childAge: new FormControl(this.scheduleInfo?.childAge ?? undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      lessonType: new FormControl(this.scheduleInfo?.lessonType ?? undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      skill: new FormControl(this.scheduleInfo?.skill ?? '', { nonNullable: true }),
      instrumentId: new FormControl(this.scheduleInfo?.instrumentId ?? undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      subInstrumentId: new FormControl(this.scheduleInfo?.subInstrumentId ?? undefined, { nonNullable: true }),
      locationId: new FormControl(this.scheduleInfo?.locationId ?? undefined, {
        nonNullable: true,
        validators: [Validators.required]
      })
    });
    if (this.scheduleInfo?.instrumentId) {
      this.getSubInstruments();
    }
  }

  setRequiredBasedOnCondition(controlName: string, required: boolean): void {
    const control = this.schedulerInfoForm.get(controlName);
    if (required) {
      control?.setValidators([Validators.required]);
    } else {
      control?.clearValidators();
    }
    control?.updateValueAndValidity();
  }

  keepOrder = (a: any, _b: any) => {
    return a;
  };
}
