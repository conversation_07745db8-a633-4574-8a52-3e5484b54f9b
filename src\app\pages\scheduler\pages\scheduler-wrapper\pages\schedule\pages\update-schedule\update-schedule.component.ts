import { CommonModule, DatePipe } from '@angular/common';
import { AfterViewInit, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { SchedulerService, StudentPlanService } from '../../services';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CBGetResponse, CBResponse, IdNameModel } from 'src/app/shared/models';
import {
  ClassTypes,
  InstructorAvaibility,
  StudentPlans,
  SuggestedTimeSlot,
  UpdateScheduleFormGroup,
  ScheduleDetailsView
} from '../../models';
import { CommonUtils } from 'src/app/shared/utils';
import { AbstractControlOptions, FormArray, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSelectModule } from '@angular/material/select';
import { GroupClassesService } from 'src/app/pages/schedule-classes/pages/group-class/services';
import { GroupClassView } from 'src/app/pages/schedule-classes/pages/group-class/models/group-class.model';
import { NgxMaterialTimepickerModule } from 'ngx-material-timepicker';
import { outOfRangeTimeValidator, timeRangeValidator } from 'src/app/shared/validators';
import { AppToasterService, NavigationService } from 'src/app/shared/services';
import { SummerCampScheduleService } from 'src/app/pages/settings/pages/summer-camp-creation/services';
import {
  AssignedInstructors,
  AssignedInstruments,
  UpdateEnsembleClassParams
} from 'src/app/pages/schedule-classes/pages/ensemble-class/models';
import { MatTooltipModule } from '@angular/material/tooltip';
import { EnsembleClassesService } from 'src/app/pages/schedule-classes/pages/ensemble-class/services';
import { Debounce } from 'src/app/shared/decorators';
import { LocalDatePipe } from 'src/app/shared/pipe';
import { DateUtils } from 'src/app/shared/utils/date.utils';
import { MultiSelectChipsComponent } from 'src/app/shared/components/multi-select-chips/multi-select-chips.component';
import { Instructor } from 'src/app/schedule-introductory-lesson/models';
import moment from 'moment';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    CommonModule,
    SharedModule,
    ReactiveFormsModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatSelectModule,
    NgxMaterialTimepickerModule,
    MatTooltipModule
  ],
  PIPES: [LocalDatePipe],
  COMPONENTS: [MultiSelectChipsComponent]
};

@Component({
  selector: 'app-update-schedule',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES, ...DEPENDENCIES.COMPONENTS],
  providers: [provideNativeDateAdapter()],
  templateUrl: './update-schedule.component.html',
  styleUrl: './update-schedule.component.scss'
})
export class UpdateScheduleComponent extends BaseComponent implements OnInit, AfterViewInit {
  @Input() selectedEvent!: ScheduleDetailsView;

  scheduleInfo!: ScheduleDetailsView | undefined;
  groupClassInfo!: GroupClassView | undefined;
  updateScheduleForm!: FormGroup<UpdateScheduleFormGroup>;
  assignedPlan!: StudentPlans | undefined;

  selectedSuggestedTimeSlotIndex!: number | undefined;
  classTypes = ClassTypes;
  instructors!: Array<Instructor>;
  suggestedTimeSlots!: SuggestedTimeSlot[] | undefined;
  selectedTimeSlot!: string | null;
  maxDate = new Date();
  transformedDate = this.datePipe.transform(this.maxDate, this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss);
  hasUserChangedInstructor = false;

  @Output() closeModal = new EventEmitter<void>();
  @Output() refreshScheduleData = new EventEmitter<void>();

  filterParams: UpdateEnsembleClassParams = {
    instructor: {
      id: 1,
      defaultPlaceholder: 'Select Instructor',
      placeholder: 'Select Instructor',
      value: [] as Array<IdNameModel>,
      totalCount: 0,
      showMax: 3,
      isOpen: false,
      showSearchBar: true,
      options: [] as Array<IdNameModel>
    }
  };

  constructor(
    protected readonly schedulerService: SchedulerService,
    private readonly groupClassService: GroupClassesService,
    private readonly ensembleClassService: EnsembleClassesService,
    private readonly studentPlanService: StudentPlanService,
    private readonly summerCampScheduleService: SummerCampScheduleService,
    private readonly cdr: ChangeDetectorRef,
    private readonly datePipe: DatePipe,
    private readonly toasterService: AppToasterService,
    private readonly navigationService: NavigationService
  ) {
    super();
  }

  ngOnInit(): void {
    this.initUpdateScheduleForm();
  }

  ngAfterViewInit(): void {
    this.hasUserChangedInstructor = false; 
    this.getDetailsBasedOnClassType();
  }

  initUpdateScheduleForm(): void {
    this.updateScheduleForm = new FormGroup<UpdateScheduleFormGroup>(
      {
        classType: new FormControl(undefined, { nonNullable: true }),
        id: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] }),
        isAllInstances: new FormControl(false, { nonNullable: true }),
        scheduleStartDate: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
        scheduleEndDate: new FormControl('', { nonNullable: true, validators: [Validators.required] }),
        daysOfSchedule: new FormArray([] as FormControl<number>[]),
        scheduleStartTime: new FormControl('', {
          nonNullable: true,
          validators: [Validators.required, outOfRangeTimeValidator()]
        }),
        scheduleEndTime: new FormControl('', {
          nonNullable: true,
          validators: [Validators.required, outOfRangeTimeValidator()]
        }),
        isNotifyClients: new FormControl(false, { nonNullable: true }),
        notes: new FormControl('', { nonNullable: true }),
        isSpecialNeedsLesson: new FormControl(false, { nonNullable: true }),
        instructorId: new FormControl(undefined, { nonNullable: true, validators: [Validators.required] })
      },
      { validators: timeRangeValidator('scheduleStartTime', 'scheduleEndTime') } as AbstractControlOptions
    );
  }

  getDetailsBasedOnClassType(): void {
    this.getScheduleDetails(this.selectedEvent);
    if (this.selectedEvent?.classType === this.classTypes.GROUP_CLASS) {
      this.getGroupClassDetail(this.selectedEvent.groupClassScheduleId);
    }
  }

  getScheduleDetails(scheduleInfo: ScheduleDetailsView): void {
    this.scheduleInfo = JSON.parse(JSON.stringify(scheduleInfo));
    if (this.scheduleInfo) {
      this.scheduleInfo.start = DateUtils.toLocal(this.scheduleInfo?.start);
      this.scheduleInfo.end = DateUtils.toLocal(this.scheduleInfo?.end);
    }
    if (scheduleInfo?.classType === this.classTypes.SUMMER_CAMP) {
      this.setUpdateSummerCampFormData();
      this.getSummerCampInstructors();
    } else if (scheduleInfo?.classType === this.classTypes.ENSEMBLE_CLASS) {
      this.setUpdateScheduleFormData();
      this.getEnsembleInstructors();
      this.getSuggestedTime(false, scheduleInfo.classType);
    } else {
      this.setUpdateScheduleFormData();
      this.getInstructors();
      this.getStudentPlans(scheduleInfo?.studentDetails[0]?.studentId);
      this.getSuggestedTime(false, scheduleInfo.classType);
    }
  }

  getGroupClassDetail(id: number): void {
    this.showPageLoader = true;
    this.groupClassService
      .get<CBGetResponse<GroupClassView>>(`${API_URL.groupClassScheduleSummaries.getGroupClassScheduleSummaryForView}?id=${id}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<GroupClassView>) => {
          this.groupClassInfo = res.result;
          this.setUpdateGroupClassFormData();
          this.getInstructors();
          this.getSuggestedTime(false, this.classTypes.GROUP_CLASS);
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getStudentPlans(studentId: number): void {
    if (!studentId) {
      return;
    }
    this.showPageLoader = true;
    this.studentPlanService
      .getList<CBResponse<StudentPlans>>(`${API_URL.studentPlans.getStudentPlans}?DependentInformationId=${studentId}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<StudentPlans>) => {
          this.assignedPlan = res.result.items.find(item => item.studentplan.id === this.scheduleInfo?.planId);
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  findVisitsPerWeekInPlan(plan: StudentPlans | undefined): number {
    return plan?.planDetails[0].planDetail.visitsPerWeek!;
  }

  setUpdateScheduleFormData(): void {
    this.updateScheduleForm.patchValue({
      ...this.scheduleInfo,
      scheduleStartDate: this.scheduleInfo?.start,
      scheduleEndDate: this.scheduleInfo?.end,
      scheduleEndTime: this.scheduleInfo?.end,
      scheduleStartTime: this.scheduleInfo?.start,
      instructorId: this.selectedEvent?.instructorId || this.selectedEvent?.assignedInstructors?.map(instructor => instructor.id),
      assignedInstructors: this.selectedEvent?.assignedInstructors?.map(instructor => instructor.id)
    });
    this.filterParams.instructor.value = this.selectedEvent?.assignedInstructors ? [...this.selectedEvent.assignedInstructors] : [];
    this.selectedTimeSlot = `${this.datePipe.transform(this.scheduleInfo?.start, 'shortTime')} - ${this.datePipe.transform(
      this.scheduleInfo?.end,
      'shortTime'
    )}`;
  }

  setUpdateGroupClassFormData(): void {
    const groupClassInfo = this.groupClassInfo?.groupClassScheduleSummary;
    this.updateScheduleForm.patchValue({
      ...groupClassInfo,
      daysOfSchedule: [Number(groupClassInfo?.scheduleDays)],
      scheduleStartDate: this.updateScheduleForm.getRawValue().isAllInstances
        ? DateUtils.toLocal(groupClassInfo?.scheduleStartDate)
        : DateUtils.toLocal(this.scheduleInfo?.scheduleDate),
      scheduleEndDate: this.updateScheduleForm.getRawValue().isAllInstances
        ? DateUtils.toLocal(groupClassInfo?.scheduleEndDate)
        : DateUtils.toLocal(this.scheduleInfo?.scheduleDate),
      instructorId: this.scheduleInfo?.instructorId,
      scheduleStartTime: this.scheduleInfo?.start,
      scheduleEndTime: this.scheduleInfo?.end
    });
    this.selectedTimeSlot = `${this.datePipe.transform(this.scheduleInfo?.start, 'shortTime')} - ${this.datePipe.transform(
      this.scheduleInfo?.end,
      'shortTime'
    )}`;
  }

  getInstructors(): void {
    if (
      this.getInstructorAvailability.scheduleStartDate &&
      this.getInstructorAvailability.locationId &&
      this.getInstructorAvailability.instrumentId &&
      (this.getInstructorAvailability.classType !== ClassTypes.RECURRING || this.getInstructorAvailability.planId)
    ) {
      this.schedulerService
        .add(this.getInstructorAvailability, API_URL.scheduleLessonDetails.getAllInstructor)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: CBGetResponse<Instructor[]>) => {
            this.instructors = response.result;
            this.cdr.detectChanges();
          },
          error: () => {
            this.cdr.detectChanges();
          }
        });
    }
  }

  getEnsembleInstructors(): void {
    if (
      this.getInstructorAvailability.scheduleStartDate &&
      this.getInstructorAvailability.locationId &&
      this.getInstructorAvailability.instrumentIds
    ) {
      this.ensembleClassService
        .add(this.getInstructorAvailability, API_URL.ensembleClassesScheduleSummaries.instructorAvailabilities)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: CBGetResponse<Instructor[]>) => {
            this.instructors = response.result;
            this.filterParams.instructor.options = response.result;
            this.filterParams.instructor.totalCount = response.result.length;
            this.cdr.detectChanges();
          },
          error: () => {
            this.cdr.detectChanges();
          }
        });
    }
  }

  getSummerCampInstructors(): void {
    const control = this.getInstructorAvailability;
    if (
      control.locationId &&
      control.scheduleStartDate &&
      control.scheduleEndDate &&
      control.scheduleStartTime &&
      control.scheduleEndTime
    ) {
      this.summerCampScheduleService
        .add(this.getInstructorAvailability, API_URL.summerCampScheduleSummaries.getAvailableInstructors)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response: CBGetResponse<Instructor[]>) => {
            this.instructors = response.result;
            this.cdr.detectChanges();
          }
        });
    }
  }

  setUpdateSummerCampFormData(): void {
    this.updateScheduleForm.patchValue({
      ...this.scheduleInfo,
      scheduleStartDate: DateUtils.toLocal(this.scheduleInfo?.scheduleDate),
      scheduleEndDate: DateUtils.toLocal(this.scheduleInfo?.scheduleDate),
      scheduleEndTime: this.datePipe.transform(this.scheduleInfo?.end, 'shortTime') ?? '',
      scheduleStartTime: this.datePipe.transform(this.scheduleInfo?.start, 'shortTime') ?? '',
      instructorId: this.scheduleInfo?.instructorId,
      assignedInstructors: this.scheduleInfo?.assignedInstructors?.map(instructor => instructor.id)
    });
    this.setTimeInit('scheduleStartTime');
    this.setTimeInit('scheduleEndTime');
  }

  setTimeInit(control: string): void {
    const startDate = this.datePipe.transform(
      this.updateScheduleForm.controls.scheduleStartDate.value,
      this.constants.dateFormats.yyyy_MM_dd
    );
    const timeValue = this.datePipe.transform(
      new Date(`${startDate} ${this.updateScheduleForm.get(control)?.value}`),
      this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss
    );

    this.updateScheduleForm?.get(control)?.setValue(timeValue);
  }

  setInstructorIdDetails(): void {
    const instructor = this.filterParams.instructor.value.map(instructor => instructor.id);
    this.setFormControlValue('instructorId', instructor);
    this.hasUserChangedInstructor = true; 
    this.getSuggestedTime(false, this.selectedEvent?.classType);
  }

  resetInstructor(): void {
    this.updateScheduleForm.controls.instructorId?.reset();
    this.updateScheduleForm.controls.assignedInstructors?.clear();
    this.filterParams.instructor.value = [];
    if (this.selectedEvent && this.selectedEvent.classType === this.classTypes.SUMMER_CAMP) {
      this.getSummerCampInstructors();
    } else {
      this.getInstructors();
    }
    this.resetSuggestedTime();
  }

  get getInstructorAvailability(): InstructorAvaibility {
    const getDayFromDate = moment(this.updateScheduleForm.getRawValue().scheduleStartDate).day();
    const duration = this.getTimeDiff(this.selectedEvent?.start!, this.selectedEvent?.end!);
    const startTime = CommonUtils.combineDateAndTime(
      this.updateScheduleForm.getRawValue().scheduleStartDate ?? '',
      this.updateScheduleForm.getRawValue().scheduleStartTime
    );
    const endTime = CommonUtils.combineDateAndTime(
      this.updateScheduleForm.getRawValue().scheduleStartDate ?? '',
      this.updateScheduleForm.getRawValue().scheduleEndTime
    );
    return {
      classType: this.selectedEvent?.classType,
      scheduleStartDate: DateUtils.getUtcRangeForLocalDate(this.updateScheduleForm.controls.scheduleStartDate.value ?? '').startUtc,
      scheduleEndDate:
        this.selectedEvent?.classType === this.classTypes.GROUP_CLASS && this.updateScheduleForm.getRawValue().isAllInstances
          ? DateUtils.getUtcRangeForLocalDate(this.updateScheduleForm.controls.scheduleEndDate.value ?? '').endUtc
          : DateUtils.getUtcRangeForLocalDate(this.updateScheduleForm.controls.scheduleStartDate.value ?? '').endUtc,
      daysOfSchedule: this.updateScheduleForm.controls.isAllInstances.value
        ? this.updateScheduleForm?.controls.daysOfSchedule.value
        : [getDayFromDate],
      isAllInstances: this.updateScheduleForm?.controls.isAllInstances.value ?? false,
      instructorId: this.updateScheduleForm.controls.instructorId?.value,
      planId: this.scheduleInfo?.planId ?? null,
      locationId: this.selectedEvent?.locationId,
      duration: this.groupClassInfo?.groupClassScheduleSummary.duration ?? duration,
      studentId: null,
      instrumentId: this.selectedEvent?.instrumentId,
      instrumentIds: this.scheduleInfo?.assignedInstruments?.map(item => item.instrumentId) ?? [],
      instructorIds: this.filterParams.instructor.value.map(item => item.id),
      scheduleStartTime: startTime,
      scheduleEndTime: endTime
    };
  }

  resetSuggestedTime(): void {
    this.suggestedTimeSlots = [];
    this.selectedTimeSlot = null;
    this.getSuggestedTime(true, this.selectedEvent?.classType);
  }

  @Debounce(500)
  getSuggestedTime(clearScheduleDate: boolean, type: number): void {
    if (
      this.getInstructorAvailability.scheduleStartDate &&
      this.getInstructorAvailability.scheduleEndDate &&
      (this.getInstructorAvailability.instructorId || this.getInstructorAvailability.instructorIds?.length)
    ) {
      if (type === this.classTypes.ENSEMBLE_CLASS) {
        this.ensembleClassService
          .add(this.getInstructorAvailability, API_URL.ensembleClassesScheduleSummaries.getInstructorAvaibilableTimeSlots)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: (response: CBGetResponse<SuggestedTimeSlot[]>) => {
              this.suggestedTimeSlots = response.result.map(timeSlot => ({
                ...timeSlot,
                startTime: DateUtils.toLocal(timeSlot.startTime),
                endTime: DateUtils.toLocal(timeSlot.endTime)
              }));
              if (clearScheduleDate) {
                this.selectedTimeSlot = null;
                this.updateScheduleForm.controls.scheduleStartTime.reset();
              }
              this.updateScheduleForm.controls.instructorId?.setErrors(null);

              if (
                this.hasUserChangedInstructor &&
                !this.suggestedTimeSlots?.find(timeSlot => timeSlot.startTime.split('T')[1] == this.updateScheduleForm.getRawValue().scheduleStartTime.split('T')[1])
              ) {
                this.updateScheduleForm.controls.instructorId?.setErrors({ instructorChanged: true });
                this.updateScheduleForm.controls.instructorId?.markAsTouched();
              }
              this.cdr.detectChanges();
            }
          });
      } else if (this.selectedEvent.classType !== this.classTypes.SUMMER_CAMP) {
        this.schedulerService
          .add(this.getInstructorAvailability, API_URL.scheduleLessonDetails.getInstructorAvaibility)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: (response: CBGetResponse<SuggestedTimeSlot[]>) => {
              this.suggestedTimeSlots = response.result.map(timeSlot => ({
                ...timeSlot,
                startTime: DateUtils.toLocal(timeSlot.startTime),
                endTime: DateUtils.toLocal(timeSlot.endTime)
              }));
              if (clearScheduleDate) {
                this.selectedTimeSlot = null;
                this.updateScheduleForm.controls.scheduleStartTime.reset();
              }
              this.updateScheduleForm.controls.instructorId?.setErrors(null);

              if (
                this.hasUserChangedInstructor &&
                !this.suggestedTimeSlots?.find(timeSlot => timeSlot.startTime.split('T')[1] === this.updateScheduleForm.getRawValue().scheduleStartTime.split('T')[1])
              ) {
                this.updateScheduleForm.controls.instructorId?.setErrors({ isInstructorAvailable: true });
                this.updateScheduleForm.controls.instructorId?.markAsTouched();
              }
              this.cdr.detectChanges();
            }
          });
      }
    }
  }

  setTimeOnDateChange(control: string): void {
    const startDate = this.datePipe.transform(
      this.updateScheduleForm.controls.scheduleStartDate.value,
      this.constants.dateFormats.yyyy_MM_dd
    );
    const formattedTime = this.datePipe.transform(this.updateScheduleForm.get(control)?.value, 'shortTime');
    const timeValue = CommonUtils.combineDateAndTime(startDate ?? '', formattedTime ?? '');

    this.updateScheduleForm?.get(control)?.setValue(timeValue);
  }

  clearScheduleEndDate(): void {
    if (this.updateScheduleForm.controls.isAllInstances.value && this.selectedEvent?.groupClassScheduleId) {
      this.updateScheduleForm.controls.scheduleEndDate.reset();
    }
    if (this.selectedEvent?.classType !== this.classTypes.SUMMER_CAMP) {
      this.getSuggestedTime(true, this.selectedEvent?.classType);
    } else {
      this.updateScheduleForm.controls.instructorId?.reset();
      this.setTimeOnDateChange('scheduleStartTime');
      this.setTimeOnDateChange('scheduleEndTime');
    }
  }

  setStartAndEndTime(selectedTimeSlot: SuggestedTimeSlot): void {
    this.updateScheduleForm.controls.instructorId?.setErrors(null);
    const startShortTime = this.datePipe.transform(selectedTimeSlot.startTime, 'shortTime');
    const endShortTime = this.datePipe.transform(selectedTimeSlot.endTime, 'shortTime');
    this.selectedTimeSlot = `${startShortTime} - ${endShortTime}`;
    this.updateScheduleForm.patchValue({
      scheduleStartTime: selectedTimeSlot.startTime,
      scheduleEndTime: selectedTimeSlot.endTime
    });
  }

  onUpdateSchedule(): void {
    const scheduleValue = this.updateScheduleForm.getRawValue();
    const getDayFromDate = moment(scheduleValue.scheduleStartDate).day();
    if (this.updateScheduleForm.invalid) {
      this.updateScheduleForm.markAllAsTouched();
      return;
    }
    this.updateScheduleForm.markAsUntouched();
    this.showBtnLoader = true;
    this.schedulerService
      .update(
        {
          ...scheduleValue,
          ...this.selectedEvent,
          scheduleStartDate: DateUtils.getUtcRangeForLocalDate(scheduleValue.scheduleStartDate ?? '').startUtc,
          scheduleEndDate: scheduleValue.isAllInstances
            ? DateUtils.getUtcRangeForLocalDate(scheduleValue.scheduleEndDate ?? '').endUtc
            : DateUtils.getUtcRangeForLocalDate(scheduleValue.scheduleStartDate ?? '').endUtc,
          scheduleStartTime: CommonUtils.combineDateAndTime(scheduleValue.scheduleStartDate ?? '', scheduleValue.scheduleStartTime),
          daysOfSchedule: scheduleValue.isAllInstances ? scheduleValue.daysOfSchedule : [getDayFromDate],
          scheduleEndTime: CommonUtils.combineDateAndTime(scheduleValue.scheduleStartDate ?? '', scheduleValue.scheduleEndTime),
          instructorId:
            scheduleValue.classType !== this.classTypes.ENSEMBLE_CLASS
              ? [scheduleValue.instructorId]
              : this.filterParams.instructor.value.flatMap(item => item.id)
        },
        API_URL.crud.update
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.onCloseModal();
          this.refreshScheduleData.emit();
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  openInstructorDetails(instructorId: number): void {
    this.navigationService.navigateToInstructorDetail(instructorId);
  }

  openStudentDetails(studentId: number): void {
    this.navigationService.navigateToStudentDetail(studentId);
  }

  getInstructorNames(array: AssignedInstructors[]) {
    return array
      .slice(1)
      .map(item => item.name)
      .join(', ');
  }

  getInstrumentNames(array: AssignedInstruments[]) {
    return array
      .slice(1)
      .map(item => item.instrumentName)
      .join(', ');
  }

  setDaysOfWeek(dayValue: number): void {
    const daysOfSchedule = this.updateScheduleForm.get('daysOfSchedule') as FormArray;
    const index = daysOfSchedule.value.indexOf(dayValue);

    if (this.selectedEvent?.classType === this.classTypes.GROUP_CLASS) {
      this.setFormControlValue('daysOfSchedule', [Number(dayValue)]);
    } else if (this.selectedEvent?.classType === this.classTypes.ENSEMBLE_CLASS) {
      this.setFormControlValue('daysOfSchedule', [Number(dayValue)]);
    } else {
      if (index !== -1) {
        daysOfSchedule.removeAt(index);
      } else if (daysOfSchedule.length < this.findVisitsPerWeekInPlan(this.assignedPlan)) {
        daysOfSchedule.push(new FormControl(dayValue, { nonNullable: true }));
      } else {
        this.toasterService.error(this.constants.errorMessages.maxDaysSelected);
        return;
      }
    }

    this.getSuggestedTime(false, this.selectedEvent?.classType);
  }

  isDaySelected(dayValue: number): boolean | undefined {
    const daysOfSchedule = this.updateScheduleForm.get('daysOfSchedule')?.value;
    return daysOfSchedule?.includes(dayValue);
  }

  setFormControlValue(controlName: string, value: number | string | boolean | number[]): void {
    (this.updateScheduleForm.controls as any)[controlName].setValue(value);
  }

  onRecurringEditOptionChange(editSeries: boolean, type: number): void {
    this.setRequiredBasedOnCondition('daysOfSchedule', editSeries);
    this.updateScheduleForm.controls.daysOfSchedule.clear();

    if (editSeries) {
      if (this.scheduleInfo?.daysOfSchedule) {
        this.scheduleInfo.daysOfSchedule.forEach(day => {
          this.updateScheduleForm.controls.daysOfSchedule.push(new FormControl(Number(day), { nonNullable: true }));
        });
      }

      if (this.groupClassInfo?.groupClassScheduleSummary?.scheduleDays) {
        const scheduleDays = this.groupClassInfo.groupClassScheduleSummary.scheduleDays;
        this.updateScheduleForm.controls.daysOfSchedule.push(new FormControl(Number(scheduleDays), { nonNullable: true }));
      }
    }
    if (type === this.classTypes.GROUP_CLASS) {
      this.setUpdateGroupClassFormData();
    }
    this.getSuggestedTime(false, type);
  }

  setRequiredBasedOnCondition(controlName: string, required: boolean): void {
    const control = this.updateScheduleForm.get(controlName);
    if (required) {
      control?.setValidators([Validators.required]);
    } else {
      control?.clearValidators();
    }
    control?.updateValueAndValidity();
  }

  resetAddScheduleForm(): void {
    this.updateScheduleForm.reset();
    this.updateScheduleForm.controls.daysOfSchedule.clear();
  }

  getTimeDiff(start: string, end: string): number | null {
    return CommonUtils.calculateTimeDifference(new Date(start), new Date(end));
  }

  onCloseModal(): void {
    this.resetAddScheduleForm();
    this.closeModal.emit();
  }
}
