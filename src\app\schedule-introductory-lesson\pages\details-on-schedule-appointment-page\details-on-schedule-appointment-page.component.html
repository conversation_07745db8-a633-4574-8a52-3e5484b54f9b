<div class="sil-details-wrapper">
  <div class="sil-title">Book Introductory Lesson</div>
  <div class="schedule-info-wrapper">
    <div class="text-content">Schedule Information</div>
    <div class="update-info-btn" (click)="goBackToBasicProfilePage()">Update</div>
  </div>
  @if (slotOrStaffDetails) {
    <ng-container
      [ngTemplateOutlet]="slotOrStaffDetails.slotDetails ? appointmentOrStaffDetails : noDataFound"></ng-container>
  }
</div>

<ng-template #appointmentOrStaffDetails>
  <div class="scheduler-details-wrapper">
    <div class="schedule-details-title">
      {{ slotOrStaffDetails?.slotDetails?.lessonType ? "Duet™  Virtual" : "In-person" }}
      {{ getInstrumentNameFromValue(slotOrStaffDetails?.slotDetails?.instrumentId) }} Lesson (<span *ngIf="slotOrStaffDetails?.slotDetails?.skill">{{ slotOrStaffDetails?.slotDetails?.skill }},</span>{{ schedulerService.getAgeLabelFromValue(slotOrStaffDetails?.slotDetails?.childAge) }})
    </div>
    <div class="location">
      <div class="location-icon">
        <img [src]="constants.staticImages.icons.location" alt="" />
      </div>
      <div class="location-text">{{ getLocationNameFromValue(slotOrStaffDetails?.slotDetails?.locationId) }}</div>
    </div>
  </div>
  <ng-container
    [ngTemplateOutlet]="
      slotOrStaffDetails && slotOrStaffDetails.showStaffDetails ? staffDetails : appointmentDetails
    "></ng-container>
</ng-template>

<ng-template #appointmentDetails>
  <div class="course-basic-details-wrapper">
    <div class="course-basic-detail-item">
      <img [src]="constants.staticImages.icons.timeCircleClock" alt="" />
      <div class="course-basic-detail-text">30 Min</div>
    </div>
    <div class="course-basic-detail-item">
      <img [src]="constants.staticImages.icons.profileCircle" alt="" />
      <div class="course-basic-detail-text">
        {{ schedulerService.getAgeLabelFromValue(slotOrStaffDetails?.slotDetails?.childAge) }} Age
      </div>
    </div>
  </div>
  <div class="course-desc-wrapper">
    <p>
      You're only a few clicks (taps on mobile) away from scheduling your introductory lesson at Octopus Music School
      (lucky you)!
    </p>
    <p>
      After your lesson is over, stop by the front desk to sign up for lessons on a weekly basis. If you're not quite
      ready to take the plunge, there is no further obligation of any kind!
    </p>
  </div>
</ng-template>

<ng-template #staffDetails>
  <div class="schedule-info-wrapper">
    <div class="text-content">Selected Instructor Bio</div>
    <div class="update-info-btn" (click)="showAppointmentDetails()">Close</div>
  </div>
  <div class="staff-img">
    <img
      [src]="
        slotOrStaffDetails?.slotDetails?.instructorDetail?.profilePhoto?.length
          ? slotOrStaffDetails?.slotDetails?.instructorDetail?.profilePhoto
          : constants.staticImages.images.placeholderImage
      "
      alt="" />
  </div>
  <p class="staff-name">{{ slotOrStaffDetails?.slotDetails?.instructorDetail?.name }}</p>
  <p class="course-desc-wrapper mb-0">
    {{ slotOrStaffDetails?.slotDetails?.instructorDetail?.bio }}
  </p>
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found">Please select an appointment to view the details.</div>
</ng-template>
