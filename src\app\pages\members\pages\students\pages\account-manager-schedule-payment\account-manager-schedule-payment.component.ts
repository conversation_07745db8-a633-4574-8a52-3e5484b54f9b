import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { Subscription, takeUntil } from 'rxjs';
import { AuthService } from 'src/app/auth/services';
import { SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { CardDetailsResponse, PaymentParams } from 'src/app/pages/schedule-classes/pages/introductory-lesson/models';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { ClassTypes, LessonTypes } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { Instrument } from 'src/app/request-information/models';
import { InstructorList } from 'src/app/schedule-introductory-lesson/models';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CBGetResponse, CBResponse } from 'src/app/shared/models';
import { CommonService, AppToasterService } from 'src/app/shared/services';
import { SharedModule } from 'src/app/shared/shared.module';
import { PaymentMethodsComponent } from '../../../../../../shared/components/payment-methods/payment-methods.component';
import { Account } from 'src/app/auth/models/user.model';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { TransactionTypes } from 'src/app/pages/shop/models';
import { MatSidenavModule } from '@angular/material/sidenav';
import { LocalDatePipe } from 'src/app/shared/pipe';
import { CommonUtils } from 'src/app/shared/utils';
import { DateUtils } from 'src/app/shared/utils/date.utils';

const DEPENDENCIES = {
  MODULES: [CommonModule, MatButtonModule, SharedModule, MatFormFieldModule, FormsModule, MatInputModule, MatIconModule, MatSidenavModule],
  COMPONENTS: [PaymentMethodsComponent],
  PIPES: [LocalDatePipe]
};

@Component({
  selector: 'app-account-manager-schedule-payment',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS, ...DEPENDENCIES.PIPES],
  templateUrl: './account-manager-schedule-payment.component.html',
  styleUrl: './account-manager-schedule-payment.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AccountManagerSchedulePaymentComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() scheduleInfo!: any;
  @Input() isScheduleMakeUpLesson!: boolean;
  @Input() selectedDependentId!: number | undefined;
  @Input() accManagerDetails!: Account | null;
  @Input() convertToUtc!: boolean;

  private readonly subscriptions = new Subscription();
  instruments!: Array<Instrument>;
  locations!: Array<SchoolLocations>;
  instructors!: Array<InstructorList>;
  cardDetails!: CardDetailsResponse;
  selectedLocation!: string;
  selectedInstructorName!: string;
  selectedInstrumentName!: string;
  isPaymentDone = false;
  isRePayment = false;
  classTypes = ClassTypes;
  lessonTypes = LessonTypes;
  startTime!: string;
  endTime!: string;

  discountAmount: number = 0;
  discountedPrice: number = 0;
  showDiscountField: boolean = false;
  discountError: string | null = null;
  totalPayableAmount: number = 0;
  isAddressIncomplete = false;

  @Output() closeEnrollmentSideNav = new EventEmitter<void>();
  @Output() goBack = new EventEmitter<void>();
  @Output() confirmAppointments = new EventEmitter<void>();
  @Output() scheduleIntroductoryLesson = new EventEmitter<any>();
  @Output() rePaymentForTheSchedule = new EventEmitter<any>();

  constructor(
    private readonly commonService: CommonService,
    private readonly cdr: ChangeDetectorRef,
    private readonly authService: AuthService,
    protected readonly schedulerService: SchedulerService,
    private readonly toasterService: AppToasterService,
    private readonly paymentService: PaymentService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCurrentUserDetails();
    this.getLocations();
    this.getInstruments();
    this.getInstructors();
    this.getCardDetailsForPaymentInitiate();
    this.calculateTotalPayableAmount();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['scheduleInfo']?.currentValue) {
      this.scheduleInfo = changes['scheduleInfo']?.currentValue;

      if (this.convertToUtc) {
        this.startTime = DateUtils.toUTC(this.scheduleInfo.start, 'yyyy-MM-DDTHH:mm:ss');
        this.endTime = DateUtils.toUTC(this.scheduleInfo.end, 'yyyy-MM-DDTHH:mm:ss');
      } else {
        this.startTime = this.scheduleInfo.start;
        this.endTime = this.scheduleInfo.end;
      }
    }
  }

  clearDiscount(): void {
    this.showDiscountField = false;
    this.discountAmount = 0;
    this.discountError = null;

    if (this.constants.roleIds.CLIENT !== this.currentUser?.userRoleId) {
      this.calculateTotalPayableAmount();
    }
  }

  onDiscountChange(): void {
    const maxPrice: number = this.scheduleInfo.price ?? this.constants.defaultAmountPayableForIntroductoryClasses;

    this.discountError = null;

    // Validate input
    if (this.discountAmount < 0) {
      this.discountAmount = 0;
      this.discountError = 'Discount cannot be negative';
    } else if (this.discountAmount > maxPrice) {
      this.discountAmount = maxPrice;
      this.discountError = `Discount cannot exceed $${maxPrice.toFixed(2)}`;
    }

    this.calculateTotalPayableAmount();
  }

  calculateTotalPayableAmount(): void {
    const originalPrice = this.scheduleInfo.price ?? this.constants.defaultAmountPayableForIntroductoryClasses;
    this.discountedPrice = originalPrice - this.discountAmount;
    this.totalPayableAmount = this.discountedPrice;
    // this.totalPayableAmount = this.discountedPrice + this.registrationFee + this.securityDeposit;
  }

  getCardDetailsForPaymentInitiate(): void {
    this.subscriptions.add(
      this.paymentService.userCardDetails$.pipe(takeUntil(this.destroy$)).subscribe(card => {
        if (card) {
          this.cardDetails = card;
          this.onPaymentConfirmation();
          this.cdr.detectChanges();
        }
      })
    );
  }

  getCurrentUserDetails(): void {
    this.authService
      .getCurrentUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res;
          this.cdr.detectChanges();
        }
      });
  }

  getInstruments(): void {
    this.commonService
      .getInstruments()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          this.instruments = res.result.items;
          this.getInstrumentNameFromValue(this.scheduleInfo.instrumentId);
          this.cdr.detectChanges();
        }
      });
  }

  getInstructors(): void {
    this.commonService
      .getInstructors()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: CBResponse<InstructorList>) => {
          this.instructors = response.result.items;
          this.getInstructorNameFromValue(this.scheduleInfo.instructorId);
          this.cdr.detectChanges();
        }
      });
  }

  getLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.getLocationNameFromValue(this.scheduleInfo.locationId);
          this.cdr.detectChanges();
        }
      });
  }

  onScheduleMakeUpClass(): void {
    this.confirmAppointments.emit();
  }

  onPaymentDone(): void {
    this.showBtnLoader = true;
    setTimeout(() => {
      //tobe use
      // if (this.cardConnectPaymentComponent.isCardInvalid) {
      //   this.toasterService.error(this.constants.errorMessages.invalidCardDetails);
      //   // this.cardConnectPaymentComponent.resetTokenFrame();
      // } else {
      //   // this.cardConnectPaymentComponent.tokenize();
      //   this.cardDetails = this.cardConnectPaymentComponent.cardDetails;
      //   this.isPaymentDone = true;
      //   this.cdr.detectChanges();
      // }
      this.showBtnLoader = false;
      this.cdr.detectChanges();
    }, 1000);
  }

  onCloseModal(): void {
    this.showBtnLoader = false;
    this.closeEnrollmentSideNav.emit();
  }

  goBackFn(): void {
    this.goBack.emit();
  }

  isAddressIncompleteFn(): void {
    this.paymentService.isAddressIncomplete$.pipe(takeUntil(this.destroy$)).subscribe(isIncomplete => {
      this.isAddressIncomplete = isIncomplete;
      this.cdr.markForCheck();
    });
  }

  onPaymentConfirmation(): void {
    Object?.assign(this.cardDetails, {
      paidAmount: this.totalPayableAmount,
      discountedAmount: +this.discountAmount,
      totalAmount: this.scheduleInfo.price ?? this.constants.defaultAmountPayableForIntroductoryClasses
    });

    if (this.isRePayment && this.scheduleInfo.classType === ClassTypes.INTRODUCTORY) {
      this.rePaymentForTheSchedule.emit(this.cardDetails);
      return;
    }

    if (this.scheduleInfo.classType === ClassTypes.INTRODUCTORY) {
      this.scheduleIntroductoryLesson.emit(this.cardDetails);
      return;
    }

    this.initPaymentForGroupAndSummerCamp();
  }

  initPaymentForGroupAndSummerCamp(): void {
    this.isAddressIncompleteFn();
    if (this.isAddressIncomplete) {
      this.toasterService.error(this.constants.errorMessages.addressIncomplete);
      return;
    }
    this.paymentService.showBtnLoader(true);
    if (this.cardDetails.isUsingSavedCard === false) {
      this.paymentService.add(this.getPaymentParams(), API_URL.payment.NMIPayment).subscribe({
        next: res => {
          this.toasterService.success(this.constants.successMessages.paymentSuccess);
          this.isPaymentDone = true;
          this.isRePayment = false;
          this.confirmAppointments.emit();
          this.paymentService.showBtnLoader(false);
          this.cdr.detectChanges();
        },
        error: () => {
          this.isPaymentDone = false;
          this.isRePayment = true;
          this.paymentService.showBtnLoader(false);
          this.cdr.detectChanges();
        }
      });
    } else {
      this.paymentService
        .add(this.getCardDetails(), API_URL.payment.paymentUsingSavedCard)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.isPaymentDone = true;
            this.isRePayment = false;
            this.toasterService.success(this.constants.successMessages.paymentSuccess);
            this.confirmAppointments.emit();
            this.paymentService.showBtnLoader(false);
            this.cdr.detectChanges();
          },
          error: () => {
            this.isPaymentDone = false;
            this.isRePayment = true;
            this.paymentService.showBtnLoader(false);
            this.cdr.detectChanges();
          }
        });
    }
  }

  getCardDetails(): PaymentParams {
    return {
      userId: this.currentUser?.userId,
      dependentInformationId: this.selectedDependentId ?? this.currentUser?.dependentId,
      classType: this.scheduleInfo.classType,
      scheduleId: this.scheduleInfo.id,
      amount: this.scheduleInfo.price,
      paidDate: new Date(),
      transactionType: TransactionTypes.CARD,
      paidAmount: this.totalPayableAmount,
      totalAmount: this.scheduleInfo.price,
      discountedAmount: this.discountAmount
    };
  }

  getPaymentParams(): PaymentParams {
    return {
      dependentInformationId: this.selectedDependentId ?? this.currentUser?.dependentId,
      classType: this.scheduleInfo.classType,
      scheduleId: this.scheduleInfo.id,
      ccNum: this.cardDetails.number,
      ccExpiry: this.cardDetails.expiry,
      ccType: this.cardDetails.type,
      token: this.cardDetails.token,
      isSaveCard: this.cardDetails.isSaveCard,
      paidDate: new Date(),
      address: this.cardDetails.address,
      city: this.cardDetails.city,
      state: this.cardDetails.state,
      zip: this.cardDetails.zip,
      firstName: this.cardDetails.firstName,
      lastName: this.cardDetails.lastName,
      transactionType: TransactionTypes.CARD,
      paidAmount: this.totalPayableAmount,
      totalAmount: this.scheduleInfo.price,
      discountedAmount: this.discountAmount
    };
  }

  setIsPaymentDone(isPaymentDone: boolean): void {
    this.isPaymentDone = isPaymentDone;
    this.cdr.detectChanges();
  }

  setIsRePaymentDone(isRePayment: boolean): void {
    this.isRePayment = isRePayment;
    this.cdr.detectChanges();
  }

  getInstrumentNameFromValue(value: number | undefined) {
    this.selectedInstrumentName = this.instruments?.find(name => name.instrumentDetail.id === value)?.instrumentDetail.name || '';
    // this.cdr.detectChanges();
  }

  getLocationNameFromValue(value: number | undefined) {
    this.selectedLocation = this.locations?.find(name => name.schoolLocations.id === value)?.schoolLocations.locationName || '';
    this.cdr.detectChanges();
  }

  getInstructorNameFromValue(value: number | undefined) {
    this.selectedInstructorName = this.instructors?.find(name => name.instructorDetail.id === value)?.instructorDetail.name || '';
    this.cdr.detectChanges();
  }

  getDependentNameFromValue(value: number | undefined): string {
    return CommonUtils.getDependentNameFromValue(value, this.currentUser);
  }

  preventInvalidInput(event: KeyboardEvent): void {
    // Allow: backspace, delete, tab, escape, enter, decimal point
    const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Enter', 'Escape', 'ArrowLeft', 'ArrowRight', '.'];

    // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
    if ((event.ctrlKey && ['a', 'c', 'v', 'x'].includes(event.key.toLowerCase())) || allowedKeys.includes(event.key)) {
      return;
    }

    // Allow numbers
    if (/^\d$/.test(event.key)) {
      const input = event.target as HTMLInputElement;
      const value = input.value;
      const selectionStart = input.selectionStart || 0;
      const selectionEnd = input.selectionEnd || 0;

      // Calculate what the new value would be
      const newValue = value.substring(0, selectionStart) + event.key + value.substring(selectionEnd);
      const numericValue = parseFloat(newValue);
      const maxPrice = this.scheduleInfo.price ?? this.constants.defaultAmountPayableForIntroductoryClasses;

      // If the new value would exceed the max price, prevent the input
      if (!isNaN(numericValue) && numericValue > maxPrice) {
        event.preventDefault();
      }
    } else {
      // Not a number, prevent input
      event.preventDefault();
    }
  }
}
