<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav [opened]="isBookYourLessonSideNavOpen" mode="over" position="end" class="sidebar-w-850" [disableClose]="true">
    <ng-container *ngIf="isBookYourLessonSideNavOpen">
      <app-book-introductory-lesson
        [scheduleInfo]="bookedLessonInfo"
        [selectedDependentId]="selectedDependentId"
        (closeEnrollmentSideNav)="isBookYourLessonSideNavOpen = false"
        (confirmAppointmentsGroup)="onConfirmAppointment()"
      ></app-book-introductory-lesson>
    </ng-container>
  </mat-sidenav>

  <mat-sidenav-content>
    <div class="header-tab-with-btn">
      <div class="title">
        Schedule @switch (getClassTypeFromScheduleType()) { @case (classTypes.GROUP_CLASS) { Group Class } @case (classTypes.SUMMER_CAMP) {
        Summer Camp } @case (classTypes.ENSEMBLE_CLASS) { Ensemble Class } }
      </div>
      <div class="action-btn-wrapper">
        @if (showDependentInfo) {
        <button
          mat-raised-button
          color="accent"
          class="mat-accent-btn back-btn action-btn"
          type="button"
          (click)="setShowDependentInfo(false)"
        >
          Back
        </button>
        } @if (showDependentInfo || !currentUser?.dependentDetails?.length) {
        <button
          mat-raised-button
          color="primary"
          class="mat-primary-btn action-btn"
          type="button"
          [disabled]="selectedScheduleClassDetails?.enrolledStudents === selectedScheduleClassDetails?.studentCapacity"
          (click)="onEnrollStudent()"
        >
          Enroll
        </button>
        } @else {
        <button mat-raised-button color="primary" class="mat-primary-btn action-btn" type="button" (click)="setShowDependentInfo(true)">
          Continue
        </button>
        }
      </div>
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>

<div class="auth-page-wrapper auth-page-with-header">
  <div class="o-card">
    <div class="o-card-body">
      <div class="schedule-group-class-lesson-content-wrapper">
        <div class="row">
          <div class="col-lg-4 col-md-12 content-detail-wrapper">
            @if (selectedScheduleClassDetails) {
            <div class="group-class-detail-wrapper">
              <div class="schedule-information-title">Schedule Information</div>
              <div class="schedule-basic-details">
                @switch (scheduleClassType) { @case (scheduleClassEnum.GROUP_CLASS) {
                <div class="group-name-age">
                  {{ selectedScheduleClassDetails.groupClassName | titlecase }} ({{
                    schedulerService.getAgeLabelFromValue(selectedScheduleClassDetails.ageGroup)
                  }})
                </div>
                } @case (scheduleClassEnum.SUMMER_CAMP) {
                <div class="group-name-age">
                  {{ selectedScheduleClassDetails.campName | titlecase }} ({{
                    schedulerService.getAgeLabelFromValue(selectedScheduleClassDetails.ageGroup)
                  }})
                </div>
                } @case (scheduleClassEnum.ENSEMBLE_CLASS) {
                <div class="group-name-age">
                  {{ selectedScheduleClassDetails.ensembleClassName | titlecase }} ({{
                    schedulerService.getAgeLabelFromValue(selectedScheduleClassDetails.ageGroup)
                  }})
                </div>
                } }

                <div class="location">
                  <img class="location-icon" [src]="constants.staticImages.icons.location" alt="" />
                  <div class="location-info-text">
                    {{ selectedScheduleClassDetails.locationName }}
                  </div>
                </div>
                <div class="instructor-name">
                  <img class="instructor-icon" [src]="constants.staticImages.icons.location" alt="" />
                  <div class="instructor-name d-flex align-items-center">
                    With
                    <span class="text-black d-flex align-items-center ms-1">
                      @if(scheduleClassType === scheduleClassEnum.ENSEMBLE_CLASS) {
                      {{ selectedScheduleClassDetails.assignedInstructors![0].instructorName | titlecase }}
                      @if(selectedScheduleClassDetails.assignedInstructors.length > 1) {
                      <div class="dot d-inline-block"></div>
                      <span [matTooltip]="getInstructorNames(selectedScheduleClassDetails.assignedInstructors)"
                        >+{{ selectedScheduleClassDetails.assignedInstructors.length - 1 }}</span
                      >
                      } } @else {
                      {{ selectedScheduleClassDetails.instructorName }}
                      }
                    </span>
                  </div>
                </div>
              </div>
              <div class="schedule-dates-week-info">
                <img [src]="constants.staticImages.icons.calendarIcon" alt="" class="info-icon" />
                <div class="schedule-dates-week-content">
                  <div class="schedule-dates">
                    {{ selectedScheduleClassDetails.scheduleStartDate | localDate | date : 'mediumDate' }}
                    - {{ selectedScheduleClassDetails.scheduleEndDate | localDate | date : 'mediumDate' }}
                  </div>
                  <div class="week-info">
                    (Every {{ schedulerService.getDayOfWeek(selectedScheduleClassDetails.scheduleDays) || 'day' }} for
                    <span class="primary-color"
                      >{{
                        schedulerService.getNumberOfWeeks(
                          selectedScheduleClassDetails.scheduleStartDate | localDate,
                          selectedScheduleClassDetails.scheduleEndDate | localDate
                        )
                      }}
                      Weeks)</span
                    >
                  </div>
                </div>
              </div>
              <div class="student-capacity-schedule-time-info-wrapper" *ngIf="scheduleClassType !== scheduleClassEnum.SUMMER_CAMP">
                <img [src]="constants.staticImages.icons.instrumentIcon" alt="" class="info-icon" />
                <div class="info-text d-flex align-items-center">
                  Instrument
                  <span class="primary-color d-flex align-items-center ms-1">
                    @if(scheduleClassType === scheduleClassEnum.ENSEMBLE_CLASS) {
                    {{ selectedScheduleClassDetails.assignedInstruments![0].instrumentName }}
                    @if(selectedScheduleClassDetails.assignedInstruments.length > 1) {
                    <div class="dot d-inline-block"></div>
                    <span [matTooltip]="getInstrumentNames(selectedScheduleClassDetails.assignedInstruments)"
                      >+{{ selectedScheduleClassDetails.assignedInstruments.length - 1 }}</span
                    >
                    } } @else {
                    {{ selectedScheduleClassDetails.instrumentName }}
                    }
                  </span>
                </div>
              </div>
              <div class="student-capacity-schedule-time-info-wrapper">
                <img [src]="constants.staticImages.icons.people" alt="" class="info-icon" />
                <div class="info-text">
                  Client Capacity
                  <span class="primary-color">
                    @if(scheduleClassType === scheduleClassEnum.ENSEMBLE_CLASS) {
                    {{ selectedScheduleClassDetails.noOfEnrolledStudents }}/{{ selectedScheduleClassDetails.studentCapacity }}
                    } @else {
                    {{ selectedScheduleClassDetails.enrolledStudents }}/{{ selectedScheduleClassDetails.studentCapacity }}
                    }
                  </span>
                </div>
              </div>
              <div class="student-capacity-schedule-time-info-wrapper">
                <img [src]="constants.staticImages.icons.timeCircleClock" alt="" class="info-icon" />
                <div class="info-text">
                  {{ selectedScheduleClassDetails.scheduleStartTime | localDate | date : 'shortTime' }}
                  - {{ selectedScheduleClassDetails.scheduleEndTime | localDate | date : 'shortTime' }}
                  @if (selectedScheduleClassDetails.duration) {
                  <span class="primary-color"> ({{ selectedScheduleClassDetails.duration }} Min)</span>
                  }
                </div>
              </div>
              <div class="student-capacity-schedule-time-info-wrapper">
                <img [src]="constants.staticImages.icons.circleDollar" alt="" class="info-icon" />
                <div class="info-text">${{ selectedScheduleClassDetails.price }}</div>
              </div>
              <div class="description">
                <p>{{ selectedScheduleClassDetails.description }}</p>
              </div>
            </div>
            } @else {
            <p>
              Most clients, especially young beginners, benefit from starting off with group instruction. It is a more cost effective way to
              begin learning (on average, a 60% lower cost than individual instruction) and does not require the same commitment as private
              lessons (clients enrolling in private instruction must attend a minimum of three months). Learning an instrument always starts
              with the basics and while it is true that clients progress at different rates over time, in the beginning, note-reading,
              consistent tempo, proper technique, and interpreting simple rhythms cannot be rushed.
            </p>
            <p>
              Group classes at Octopus Music School are like nothing offered anywhere else; comprehensive, consistent, and with significant
              individual attention given to every client. Each group class is an eight-week session that finishes with an assessment
              determining whether a client is ready for the next level.
            </p>
            }
          </div>
          <div class="col-lg-8 col-md-12">
            <div class="schedule-group-class-info-form-wrapper">
              <ng-container
                [ngTemplateOutlet]="
                  showDependentInfo
                    ? selectedScheduleClassDetails?.enrolledStudents === selectedScheduleClassDetails?.studentCapacity
                      ? noSpaceInRoom
                      : showDependentInfoTemplate
                    : groupClassListWrapperAndFilters
                "
              ></ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #groupClassListWrapperAndFilters>
  <div class="filters-main-wrapper">
    @if (getClassTypeFromScheduleType() !== classTypes.SUMMER_CAMP) {
    <div class="filter-wrapper">
      <div class="filter-label">Instrument</div>
      <div class="filter-content">
        <div class="search-bar-wrapper">
          <app-multi-select
            [filterDetail]="filters.instrumentIdFilter"
            (selectedFilterValues)="getScheduleDetailBasedOnClass()"
          ></app-multi-select>
        </div>
      </div>
    </div>
    }
    <div class="filter-wrapper">
      <div class="filter-label">Location</div>
      <div class="filter-content">
        <div class="search-bar-wrapper">
          <app-multi-select
            [filterDetail]="filters.locationIdFilter"
            (selectedFilterValues)="getScheduleDetailBasedOnClass()"
          ></app-multi-select>
        </div>
      </div>
    </div>
    <div class="filter-wrapper" *ngIf="currentUser?.userType !== SignUpForOptions.YOURSELF">
      <div class="filter-label">Age</div>
      <div class="filter-content">
        <mat-form-field class="search-bar-wrapper">
          <mat-select [(ngModel)]="filters.ageGroupFilter" (selectionChange)="getScheduleDetailBasedOnClass()">
            <mat-option [value]="all.ALL">All Age Group</mat-option>
            <mat-option *ngFor="let age of constants.ageOptions" [value]="age.value"> {{ age.label }} Age Group </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>
  </div>
  <div class="group-classes-wrapper">
    <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : groupClassListTemplate"></ng-container>
  </div>
</ng-template>

<ng-template #groupClassListTemplate>
  @if (scheduleClassDetails && scheduleClassDetails.length) { @for (scheduleClassDetail of scheduleClassDetails; track $index) {
  <div
    [ngClass]="{
      'group-class-content': true,
      'selected-group-border': scheduleClassDetail?.id === selectedScheduleClassDetails?.id
    }"
    (click)="showSelectedScheduleDetails(scheduleClassDetail)"
  >
    <div class="group-class-info">
      @switch (scheduleClassType) { @case (scheduleClassEnum.GROUP_CLASS) {
      <div class="name-price">
        {{ scheduleClassDetail?.groupClassName | titlecase }} ({{ schedulerService.getAgeLabelFromValue(scheduleClassDetail?.ageGroup) }})
      </div>
      } @case (scheduleClassEnum.SUMMER_CAMP) {
      <div class="name-price">
        {{ scheduleClassDetail?.campName | titlecase }} ({{ schedulerService.getAgeLabelFromValue(scheduleClassDetail?.ageGroup) }})
      </div>
      } @case (scheduleClassEnum.ENSEMBLE_CLASS) {
      <div class="name-price">
        {{ scheduleClassDetail?.ensembleClassName | titlecase }} ({{
          schedulerService.getAgeLabelFromValue(scheduleClassDetail?.ageGroup)
        }})
      </div>
      } }
      <div class="name-price">${{ scheduleClassDetail?.price }}</div>
    </div>
    <div class="group-class-info">
      <div class="date-time">
        {{ scheduleClassDetail?.scheduleStartDate | localDate | date : 'mediumDate' }}
        - {{ scheduleClassDetail?.scheduleEndDate | localDate | date : 'mediumDate' }}
      </div>
      <div class="date-time primary-color">
        {{ scheduleClassDetail?.scheduleStartTime | localDate | date : 'shortTime' }}
        - {{ scheduleClassDetail?.scheduleEndTime | localDate | date : 'shortTime' }}
      </div>
    </div>
    <div class="group-class-info">
      <div class="repetition-week-capacity-instructor">
        <div>
          Every {{ schedulerService.getDayOfWeek(scheduleClassDetail?.scheduleDays!) || 'day' }} for
          <span class="primary-color"
            >{{
              schedulerService.getNumberOfWeeks(scheduleClassDetail?.scheduleStartDate! | localDate, scheduleClassDetail?.scheduleEndDate! | localDate)
            }}
            Weeks</span
          >
        </div>
        <div class="dot"></div>
        <div>
          Client Capacity
          <span class="primary-color">
            @if(scheduleClassType === scheduleClassEnum.ENSEMBLE_CLASS) {
            {{ scheduleClassDetail?.noOfEnrolledStudents }}/{{ scheduleClassDetail?.studentCapacity }}
            } @else {
            {{ scheduleClassDetail?.enrolledStudents }}/{{ scheduleClassDetail?.studentCapacity }}
            }
          </span>
        </div>
      </div>
      <div class="repetition-week-capacity-instructor">
        <div class="instructor-name d-flex align-items-center">
          With
          <span class="text-black d-flex align-items-center ms-1">
            @if(scheduleClassType === scheduleClassEnum.ENSEMBLE_CLASS) {
            {{ (scheduleClassDetail?.assignedInstructors)![0].instructorName | titlecase }}
            @if(scheduleClassDetail.assignedInstructors.length > 1) {
            <div class="dot d-inline-block"></div>
            <span [matTooltip]="getInstructorNames(scheduleClassDetail.assignedInstructors)"
              >+{{ scheduleClassDetail.assignedInstructors.length - 1 }}</span
            >
            } } @else {
            {{ scheduleClassDetail?.instructorName }}
            }
          </span>
        </div>
        <img [src]="constants.staticImages.images.profileImgPlaceholder" alt="" height="20px" />
      </div>
    </div>
  </div>
  } } @else {
  <div class="no-data-found-wrapper">
    <h3>NO DATA FOUND!</h3>
  </div>
  }
</ng-template>

<ng-template #showDependentInfoTemplate>
  <div class="field-wrapper">
    <label class="required">Schedule For</label>
    <div>
      <div class="btn-typed-options-wrapper">
        @if (currentUser?.userType === SignUpForOptions.YOURSELF_AND_CHILD) {
          <span [matTooltip]="selectedScheduleClassDetails?.enrolledStudentIds?.includes(currentUser?.dependentId ?? -1) ? 'Already Enrolled' : selectedScheduleClassDetails?.ageGroup !== selectedAgeGroup ? 'Age Group Not Matched' : ''">
            <div
              class="btn-typed-option btn-typed-option-wrap"
              (keydown)="$event.preventDefault()"
              (click)="selectedDependentId = currentUser?.dependentId"
              [class.active]="selectedDependentId === currentUser?.dependentId"
              [class.disabled-btn]="selectedScheduleClassDetails?.enrolledStudentIds?.includes(currentUser?.dependentId ?? -1) || selectedScheduleClassDetails?.ageGroup !== selectedAgeGroup"
            >
              {{ currentUser?.firstName | titlecase }} {{ currentUser?.lastName | titlecase }}
            </div>
          </span>
        }
        @for (dependent of currentUser?.dependentDetails; track $index) {
          <span [matTooltip]="selectedScheduleClassDetails?.enrolledStudentIds?.includes(currentUser?.dependentId ?? -1) ? 'Already Enrolled' : selectedScheduleClassDetails?.ageGroup !== getDependentAgeGroup(dependent?.dateOfBirth!) ? 'Age Group Not Matched' : ''">
            <div
              class="btn-typed-option btn-typed-option-wrap"
              (keydown)="$event.preventDefault()"
              [class.active]="selectedDependentId === dependent?.id"
              [class.disabled-btn]="selectedScheduleClassDetails?.enrolledStudentIds?.includes(dependent?.id ?? -1) || selectedScheduleClassDetails?.ageGroup !== getDependentAgeGroup(dependent?.dateOfBirth!)"
              (click)="selectedDependentId = dependent?.id"
              [matTooltip]="selectedScheduleClassDetails?.enrolledStudentIds?.includes(dependent?.id ?? -1) ? 'Already Enrolled' : ''"
            >
              {{ dependent.firstName | titlecase }} {{ dependent.lastName | titlecase }}
            </div>
          </span>
        }
      </div>
    </div>
  </div>
</ng-template>

<ng-template #noSpaceInRoom>
  <div class="no-space-found-wrapper no-data-found-wrapper">
    <h3>This camp is currently full.</h3>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
