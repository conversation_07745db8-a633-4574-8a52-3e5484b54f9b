<div class="row">
  <div class="col-md-6">
    <div class="o-card">
      <div class="card-header">
          <div class="card-title">
            <img [src]="constants.staticImages.icons.calendarIcon" alt="calendar">
            Today's Visit
          </div>
          <div class="d-flex align-items-center">
            <div class="view-all" (click)="navigateTo('/visits')">View All</div>
          </div>
      </div>
      <div class="card-content">
        <div class="timeline-container" [ngClass]="{ 'minHeight': dashboardData.scheduleLessonDetails && dashboardData.scheduleLessonDetails.length }">
          @if (dashboardData.scheduleLessonDetails.length) {
            @for (schedule of dashboardData.scheduleLessonDetails; track $index) {
              <div class="student-schedule">
                <div class="student-content mb-1">
                  <div class="schedule-date">{{ schedule.start | localDate | date : constants.fullDate }}</div>
                  <div class="fw-bold">
                    {{ schedule.start | localDate | date : constants.dateFormats.hh_mm_a }} -
                    {{ schedule.end | localDate | date : constants.dateFormats.hh_mm_a }}
                  </div>
                </div>
                <div class="student-content mb-1 justify-content-between">
                  <div [ngClass]="{ strike: schedule.isCancelSchedule }">
                    <span *ngIf="schedule.isCancelSchedule">Canceled: </span>
                    <span *ngIf="schedule.isDraftSchedule">Draft: </span>
                    @switch (schedule.classType) { @case (classTypes.GROUP_CLASS) {
                    <span>{{ schedule?.groupClassName | titlecase }}</span>}
                    @case (classTypes.ENSEMBLE_CLASS) {
                      <span>{{ schedule?.ensembleClassName | titlecase }}</span>
                    } @case (classTypes.SUMMER_CAMP) {
                    <span>{{ schedule?.campName | titlecase }}</span>
                    } @case (classTypes.MAKE_UP) {
                    <span>{{ schedule?.instrumentName }} Make-Up Lesson</span>
                    } @case (classTypes.RECURRING) {
                    <span>{{ schedule.studentDetails[0].planName }}</span>
                    } @case (classTypes.INTRODUCTORY) {
                    <span>Introductory {{ schedule?.instrumentName }} Lesson</span>
                    } @default {
                    <span>{{ schedule?.instrumentName }} Lesson</span>
                    } }
                  </div>
                </div>
                <div class="student-content">
                  <div class="student-content mb-1">
                    <img [src]="constants.staticImages.icons.location" class="black-img" alt="" />
                    <div class="location">{{ schedule.locationName }}</div>
                  </div>
                  <div class="dot"></div>
                  <div class="student-content info">
                    <img [src]="constants.staticImages.icons.profileCircle" class="black-img" alt="" />
                    <span class="me-1">Instructor</span>
                    @if (schedule.classType === classTypes.ENSEMBLE_CLASS) {
                      @for (assignedInstructors of schedule.assignedInstructors; track $index) {
                      <ng-container *ngIf="$index < 1">
                        <div class="primary-color fw-bold pointer" (click)="openInstructorDetails(assignedInstructors.instructorId)">{{ assignedInstructors?.instructorName }}</div>
                        <div class="dot hide-sm-screen-devices" *ngIf="!$last"></div>
                      </ng-container>
                      } @if (schedule.assignedInstructors!.length>1) {
                      <div class="remaining-instrument-available-count primary-color fw-bold" [matTooltip]="getInstructorNames(schedule.assignedInstructors)">
                        {{ schedule.assignedInstructors!.length - 1}}+
                      </div>
                      } } @else{
                       <div class="primary-color fw-bold pointer" (click)="openInstructorDetails(schedule.instructorId)">{{ schedule.instructorName }}</div>
                      }
                  </div>
                  @if (schedule.studentDetails.length) {
                    <div class="dot"></div>
                    <div class="student-content info">
                      <img [src]="constants.staticImages.icons.profileCircle" class="black-img" alt="" />
                      <span class="me-1">Client</span>
                        @for (student of schedule.studentDetails; track $index) {
                        <ng-container *ngIf="$index < 1">
                          <div class="primary-color fw-bold pointer" (click)="openStudentDetails(student.studentId)">{{ student?.studentName }}</div>
                          <div class="dot hide-sm-screen-devices" *ngIf="!$last"></div>
                        </ng-container>
                        } @if (schedule.studentDetails!.length>1) {
                        <div class="remaining-instrument-available-count primary-color fw-bold" [matTooltip]="getClientNames(schedule.studentDetails)">
                          {{ schedule.studentDetails!.length - 1}}+
                        </div>
                      }
                    </div>
                  }
                </div>
              </div>
               <div *ngIf="!$last" class="dotted-divider"></div>
            }
          }
          @else {
            <app-empty-state
              icon="event_available"
              title="No Visits today"
              message="There are no scheduled lessons today."
            ></app-empty-state>
          }
        </div>
      </div>
    </div>
    <div class="o-card">
      <div class="card-header">
        <div class="card-title">
          <img [src]="constants.staticImages.icons.checkedFile" alt="Request" />
          Active Passes
        </div>
        <div class="d-flex align-items-center">
          <div class="view-all" (click)="navigateToPlanPasses('Pass')">View All</div>
        </div>
      </div>
      <div class="card-content">
        @if (dashboardData.activePasses) {
          @for (pass of dashboardData.activePasses; track $index) {
            @if ($index < 2) {
              <div class="person-item">
                <div class="person-details">
                  <div class="person-name">{{ pass.passName | titlecase }} ({{ pass.duration }})</div>
                  <div class="person-info">
                    <div class="primary-color">
                      {{ pass.visits }} visit
                    </div>
                    <div class="dot"></div>
                    <div class="name">
                      For <span class="primary-color ms-1 pointer" (click)="openStudentDetails(pass.dependentId)">{{ pass.dependentName }}</span>
                    </div>
                  </div>
                </div>
                <div class="enrollment-status notEnrolled">
                  Expires {{ pass.expiryDate | date: constants.dateFormats.MMM_D_Y }}
                </div>
              </div>
              <div class="dotted-divider" *ngIf="dashboardData.activePasses && dashboardData.activePasses.length > 1 &&  $first"></div>
            }
          }
        }
        @else {
          <app-empty-state
            icon="card_membership"
            title="No Active Passes"
            message="You don't have any active passes at this time."
          ></app-empty-state>
        }
      </div>
    </div>
  </div>
  <div class="col-md-6">
    <div class="o-card">
      <div class="card-header">
        <div class="card-title">
          <img [src]="constants.staticImages.icons.wallet" alt="Wallet" />
          Upcoming Payment
        </div>
      </div>
      <div class="card-content">
        @if (dashboardData.studentDuePlan) {
          <div class="person-item">
            <div class="person-details">
              <div class="person-name">
                @switch (true) {
                  @case (dashboardData.studentDuePlan.isDDDPlan) {
                    DDD Plan ({{ dashboardData.studentDuePlan.planDetails[0].planDetail.duration }})
                  }
                  @case (dashboardData.studentDuePlan.isRentalPlan) {
                    {{ dashboardData.studentDuePlan.instrumentName }} Rental Plan {{ dashboardData.studentDuePlan.isPlanWithInsurance ? 'With Insurance' : 'Without Insurance' }}
                  }
                  @default {
                    Weekly {{ dashboardData.studentDuePlan.isEnsembleAvailable ? "Ensemble" : dashboardData.studentDuePlan.instrumentName }} Lessons ({{ planSummaryService.getPlanType(dashboardData.studentDuePlan.planType) }}-{{ planSummaryService.getPlanSummary(dashboardData.studentDuePlan.planDetails) }})
                  }
                }
              </div>
              <div class="person-info">
                @if (!dashboardData.studentDuePlan.isRentalPlan && !dashboardData.studentDuePlan.isDDDPlan) { 
                  <div class="primary-color">
                    {{ dashboardData.studentDuePlan.planDetails[0].planDetail.visitsPerWeek }} visit per week
                  </div>
                  <div class="dot"></div>
                }
                <div class="name">
                  For <span class="primary-color ms-1 pointer" (click)="openStudentDetails(dashboardData.studentDuePlan.studentplan.dependentInformationId)">{{ dashboardData.studentDuePlan.dependentName }}</span>
                </div>
              </div>
            </div>
            <div class="enrollment-status enrolled">
              ${{ dashboardData.studentDuePlan.planPrice }} at {{ dashboardData.studentDuePlan.nextRenewalDate | date: constants.dateFormats.MMM_D_Y }}
            </div>
          </div>
        }
        @else {
          <app-empty-state
            icon="payment"
            title="No Upcoming Payments"
            message="You don't have any scheduled payments at this time."
          ></app-empty-state>
        }
      </div>
    </div>
    <div class="o-card">
      <div class="card-header">
          <div class="card-title">
            <img [src]="constants.staticImages.icons.trophy" alt="trophy">
            Grade
          </div>
      </div>
      <div class="card-content">
        <div class="student-grades">
          @if (dashboardData.studentGrades.length) {
            @for (studentGrades of dashboardData.studentGrades; track $index) {
              <div class="person-item">
                <div class="person-details">
                  <div class="person-name pointer" (click)="openStudentDetails(studentGrades.studentId)">{{ studentGrades.studentName | titlecase }}</div>
                </div>
                <div class="student-grade-info">
                  {{ studentGrades.instrumentName }} <span class="primary-color">{{ studentGrades.grade }}</span>
                  <mat-icon>arrow_upward</mat-icon>
                </div>
              </div>
              <div class="dotted-divider" *ngIf="!$last"></div>
            }
          }
          @else {
            <app-empty-state
              icon="school"
              title="No Grades Available"
              message="There are no grades assigned at this time."
            ></app-empty-state>
          }
        </div>
      </div>
    </div>
    <div class="o-card">
      <div class="card-header">
        <div class="card-title">
          <img [src]="constants.staticImages.icons.checkedFile" alt="Request" />
          Active Plans
        </div>
        <div class="d-flex align-items-center">
          <div class="view-all" (click)="navigateToPlanPasses('Plan', true)">View All</div>
        </div>
      </div>
      <div class="card-content">
        @if (dashboardData.activePlans) {
          @for (plan of dashboardData.activePlans; track $index) {
            @if ($index < 2) {
              <div class="person-item">
                <div class="person-details">
                  <div class="person-name">
                    @switch (true) {
                      @case (plan.isDDDPlan) {
                        DDD Plan ({{ plan.planDetails[0].planDetail.duration }})
                      }
                      @case (plan.isRentalPlan) {
                        {{ plan.instrumentName }} Rental Plan {{ plan.isPlanWithInsurance ? 'With Insurance' : 'Without Insurance' }}
                      }
                      @default {
                        Weekly {{ plan.isEnsembleAvailable ? "Ensemble" : plan.instrumentName }} Lessons ({{ planSummaryService.getPlanType(plan.planType) }}-{{ planSummaryService.getPlanSummary(plan.planDetails) }})
                      }
                    }
                  </div>
                  <div class="person-info">
                    @if (!plan.isRentalPlan && !plan.isDDDPlan) {
                      <div class="primary-color">
                        {{ plan.planDetails[0].planDetail.visitsPerWeek }} visit per week
                      </div>
                      <div class="dot"></div>
                    }
                    <div class="name">
                      For <span class="primary-color ms-1 pointer" (click)="openStudentDetails(plan.studentplan.dependentInformationId)">{{ plan.dependentName }}</span>
                    </div>
                  </div>
                </div>
                <div class="enrollment-status enrolled">
                  Renews {{ plan.nextRenewalDate | date: constants.dateFormats.MMM_D_Y }}
                </div>
              </div>
              <div class="dotted-divider" *ngIf="dashboardData.activePlans && dashboardData.activePlans.length > 1 &&  $first"></div>
            }
          }
        }
        @else {
          <app-empty-state
            icon="assignment"
            title="No Active Plans"
            message="You don't have any active plans at this time."
          ></app-empty-state>
        }
      </div>
    </div>
  </div>
</div>

