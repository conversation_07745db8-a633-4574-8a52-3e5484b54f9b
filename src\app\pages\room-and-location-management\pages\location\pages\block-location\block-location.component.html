<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="title">{{ selectedBlockLocationDetails ? "Edit " : "Add " }}Unavailable Location</div>
    <div class="action-btn-wrapper">
      <button
        mat-raised-button
        color="accent"
        class="mat-accent-btn back-btn"
        type="button"
        (click)="closeSideNavFun()">
        Close
      </button>
      <button
        mat-raised-button
        *ngIf="selectedBlockLocationDetails"
        color="accent" 
        class="mat-accent-btn back-btn me-1"
        type="button"
        (click)="resetData()">
        Back
      </button>
      <button
        mat-raised-button
        color="primary"
        class="mat-primary-btn"
        type="button"
        (click)="onSubmit()"
        [appLoader]="showBtnLoader">
        Save
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <form [formGroup]="blockLocationFormGroup">
      <div class="field-wrapper field-with-mat-inputs">
        <label class="required">Start Date - End Date</label>
        <div class="field-content">
          <mat-form-field class="mat-start-date w-100">
            <input
              matInput
              [matDatepicker]="startPicker"
              (click)="startPicker.open()"
              formControlName="dayOffStartDate"
              (dateChange)="blockLocationFormGroup.controls.dayOffEndDate.reset()"
              placeholder="Select Start Date"
              [min]="maxDate" />
            <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
            <mat-datepicker #startPicker></mat-datepicker>
            <mat-error>
              <app-error-messages [control]="blockLocationFormGroup.controls.dayOffStartDate"></app-error-messages>
            </mat-error>
          </mat-form-field>
            <div class="dash mb-4">-</div>
            <mat-form-field class="mat-start-date w-100">
              <input
                matInput
                [matDatepicker]="endPicker"
                (click)="endPicker.open()"
                formControlName="dayOffEndDate"
                placeholder="Select End Date"
                [min]="
                  blockLocationFormGroup.controls.dayOffStartDate.value
                    ? blockLocationFormGroup.controls.dayOffStartDate.value
                    : maxDate
                " />
              <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
              <mat-datepicker #endPicker></mat-datepicker>
              <mat-error>
                <app-error-messages [control]="blockLocationFormGroup.controls.dayOffEndDate"></app-error-messages>
              </mat-error>
            </mat-form-field>
        </div>
      </div>
      <div class="field-wrapper field-with-mat-inputs">
        <label class="required">Select Location(s)</label>
        <div class="w-100">
          <app-multi-select-chips class="w-100 mat-select-custom" [filterDetail]="blockLocationParams.locationId" (selectedFilterValues)="selectedLocationIds()"></app-multi-select-chips>
          <mat-error>
              <app-error-messages [control]="blockLocationFormGroup.controls.locationIds"></app-error-messages>
          </mat-error>
        </div>
      </div>
      <div class="field-wrapper field-with-mat-inputs">
        <label class="required">Reason</label>
        <mat-form-field>
          <textarea
            matInput
            placeholder="Enter Reason"
            formControlName="reason"
            cdkTextareaAutosize
            cdkAutosizeMinRows="3"
            cdkAutosizeMaxRows="10"></textarea>
          <mat-error>
            <app-error-messages [control]="blockLocationFormGroup.controls.reason"></app-error-messages>
          </mat-error>
        </mat-form-field>
      </div>
    </form>

    <div class="dotted-divider" *ngIf="!selectedBlockLocationDetails"></div>

    <div class="location-day-off-section" *ngIf="!selectedBlockLocationDetails">
      <div class="section-title">
        <h3>Blocked Locations Details</h3>
      </div>

      <div class="location-day-off-list">
        <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : locationDayOffTemplate"></ng-container>
      </div>
    </div>
  </div>
</div>

<ng-template #locationDayOffTemplate>
  @if (locationDayOffList && locationDayOffList.length) {
    @for (dayOff of locationDayOffList; track dayOff.id; let i = $index) {
      <div class="o-card mb-3 location-day-off-card">
        <div class="o-card-body">
          <div class="card-header">
            <div class="card-number">{{ i + 1 }}.</div>
          </div>
          <div class="day-off-content">
            <div class="content-row">
              <div class="icon-wrapper">
                <img [src]="constants.staticImages.icons.calendarIcon" alt="">
              </div>
              <div class="content-text">
                {{ dayOff.dayOffStartDate | date }} - {{ dayOff.dayOffEndDate | date }}
              </div>
            </div>
            <div class="content-row">
              <div class="icon-wrapper">
                <img [src]="constants.staticImages.icons.location" alt="">
              </div>
              <div class="content-text">
                {{ dayOff.locationNames }}
              </div>
            </div>
            <div class="content-row">
              <div class="icon-wrapper">
                <img [src]="constants.staticImages.icons.questionnaire" alt="">
              </div>
              <div class="content-text">
                {{ dayOff.reason }}
              </div>
            </div>
          </div>
            <div class="edit-icon-wrapper">
              <img [src]="constants.staticImages.icons.editPenGreen" alt="Edit" class="edit-icon" matTooltip="Edit" (click)="onEditLocationDayOff(dayOff)">
            </div>
        </div>
      </div>
    }
  } @else {
    <div class="no-data-found-wrapper">
      <h3>No blocked locations found!</h3>
    </div>
  }
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>