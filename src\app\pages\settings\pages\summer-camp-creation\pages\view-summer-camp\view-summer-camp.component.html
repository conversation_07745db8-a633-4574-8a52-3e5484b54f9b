<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="title">Summer Camp Details</div>
    <div class="action-btn-wrapper">
      <button
        mat-raised-button
        color="accent"
        class="mat-accent-btn back-btn"
        type="button"
        (click)="closeViewSideNavFun()">
        Close
      </button>
      <button
        mat-raised-button
        color="accent"
        class="mat-accent-btn back-btn"
        type="button"
        (click)="deleteSummerCampConfirmation()">
        Delete
      </button>
      <button
        mat-raised-button
        *ngIf="selectedTabOption === 'Upcoming Camps'"
        color="primary"
        class="mat-primary-btn"
        type="button"
        (click)="navigateToEdit()">
        Edit
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : viewGroupClass"></ng-container>
  </div>
</div>

<ng-template #viewGroupClass>
  <div class="group-class-detail mb-3">
    <div class="group-class-content-wrapper title">
      <div class="name">
        {{ summerCampDetail?.summerCampScheduleSummary?.campName | titlecase }} ({{
          schedulerService.getAgeLabelFromValue(summerCampDetail?.summerCampScheduleSummary?.ageGroup)
        }})
      </div>
      <div class="primary-color">${{ summerCampDetail?.summerCampScheduleSummary?.price }}</div>
    </div>
    <div class="group-class-content-wrapper">
      <div>
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.questionnaire" alt="" />
          <div class="info-label">Details</div>
        </div>
        <div class="info-content w-570">
          {{ summerCampDetail?.summerCampScheduleSummary?.description | dashIfEmpty }}
        </div>
      </div>
    </div>
    <div class="group-class-content-wrapper">
      <div>
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.calendarIcon" alt="" />
          <div class="info-label">Date</div>
        </div>
        <div class="info-content">
          {{ summerCampDetail?.summerCampScheduleSummary?.scheduleStartDate | localDate | date: "mediumDate" }} -
          {{ summerCampDetail?.summerCampScheduleSummary?.scheduleEndDate | localDate | date: "mediumDate" }}
        </div>
      </div>
      <div class="w-50">
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.calendarIcon" alt="" />
          <div class="info-label">Time</div>
        </div>
        <div class="info-content">
          {{ summerCampDetail?.summerCampScheduleSummary?.scheduleStartTime | localDate | date: "shortTime" }} -
          {{ summerCampDetail?.summerCampScheduleSummary?.scheduleEndTime | localDate | date: "shortTime" }}
        </div>
      </div>
    </div>

    <div class="group-class-content-wrapper">
      <div>
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.location" alt="" />
          <div class="info-label">Location</div>
        </div>
        <div class="info-content">{{ summerCampDetail?.summerCampScheduleSummary?.locationName }}</div>
      </div>
      <div class="w-50">
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.repeatType" alt="" />
          <div class="info-label">Repeat Types</div>
        </div>
        <div class="info-content">
          Everyday for
          <span class="primary-color ms-1"
            >{{
              schedulerService.getNumberOfWeeks(
                summerCampDetail?.summerCampScheduleSummary?.scheduleStartDate! | localDate,
                summerCampDetail?.summerCampScheduleSummary?.scheduleEndDate! | localDate
              )
            }}
            weeks</span
          >
        </div>
      </div>
    </div>

    <div class="group-class-content-wrapper">
      <div>
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.safeHome" alt="" />
          <div class="info-label">Client Capacity</div>
        </div>
        <div class="info-content">{{ summerCampDetail?.summerCampScheduleSummary?.studentCapacity }}</div>
      </div>
      <div class="w-50">
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.calendarIcon" alt="" />
          <div class="info-label">Last Date of Enrollment</div>
        </div>
        <div class="info-content">
          {{ summerCampDetail?.summerCampScheduleSummary?.enrollLastDate | localDate | date: "mediumDate" }}
        </div>
      </div>
    </div>

    <div class="group-class-content-wrapper">
      <div>
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.status" alt="" />
          <div class="info-label">Revenue Category</div>
        </div>
        <div class="info-content">{{ summerCampDetail?.summerCampScheduleSummary?.revenueCategory }}</div>
      </div>
      @if (summerCampDetail?.summerCampScheduleSummary?.isWaitlistAvailable) {
        <div class="w-50">
          <div class="group-class-content">
            <img [src]="constants.staticImages.icons.checkSquare" alt="" />
            <div class="waitlist">Waitlist Available</div>
          </div>
        </div>
      }
    </div>
  </div>

  <div class="instructor-details">
    <div class="group-class-content-wrapper title">
      <div class="name">Instructor Details</div>
    </div>
    <div class="group-class-content-wrapper">
      <div>
        <div class="group-class-content">
          <img [src]="constants.staticImages.images.profileImgPlaceholder" alt="" />
          <div class="user-label">{{ summerCampDetail?.instructorDetails?.name }}</div>
        </div>
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.phone" alt="" />
          <div class="user-label">{{ summerCampDetail?.instructorDetails?.phoneNumber }}</div>
        </div>
      </div>
      <div class="w-50">
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.email" alt="" />
          <div class="user-label">{{ summerCampDetail?.instructorDetails?.email }}</div>
        </div>
      </div>
    </div>
  </div>

  @if (summerCampDetail?.scheduleStudentDetails?.length) {
    <div class="instructor-details">
      <div class="group-class-content-wrapper title">
        <div class="name">Registered Clients</div>
      </div>
      @for (studentDetail of summerCampDetail?.scheduleStudentDetails; track $index) {
        <div class="group-class-content-wrapper mb-0">
          <div>
            <div class="group-class-content mb-0">
              <img [src]="constants.staticImages.icons.profileIcon" alt="" />
              <div class="user-label">{{ studentDetail.studentName }}</div>
            </div>
            <div class="group-class-content mb-0 ms-4">
              <div class="info-label me-1">Account Manager:</div>
              <div class="user-label">{{ studentDetail.accountManagerName }}</div>
            </div>
          </div>
        </div>
        <div class="group-class-content-wrapper ms-4">
          <div class="group-class-content mb-0">
            <img [src]="constants.staticImages.icons.email" alt="" />
            <div class="user-label">{{ studentDetail.accountManagerEmail }}</div>
          </div>
          <div class="group-class-content mb-0 w-50">
            <img [src]="constants.staticImages.icons.phone" alt="" />
            <div class="user-label">{{ studentDetail.accountManagerPhoneNo }}</div>
          </div>
        </div>
        <div class="dotted-divider" *ngIf="$index < summerCampDetail?.scheduleStudentDetails?.length! - 1"></div>
      }
    </div>
  }
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
