<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="title">{{ selectedDocDetails ? "Edit " : "Add " }}Document</div>
    <div class="action-btn-wrapper">
      <button
        mat-raised-button
        color="accent"
        class="mat-accent-btn back-btn"
        type="button"
        (click)="closeSideNavFun()">
        Close
      </button>
      <button
        mat-raised-button
        color="primary"
        class="mat-primary-btn"
        type="button"
        (click)="onSubmit()"
        [appLoader]="showBtnLoader">
        Save
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <form [formGroup]="documentFormGroup">
      <mat-form-field>
        <mat-label>Document Name</mat-label>
        <input type="text" matInput formControlName="name" />
        <mat-error>
          <app-error-messages [control]="documentFormGroup.controls.name"></app-error-messages>
        </mat-error>
      </mat-form-field>
      <mat-form-field>
        <mat-label>Document Type</mat-label>
        <mat-select formControlName="documentType" placeholder="Select Document Type">
          <mat-option [value]="documentType.MAIN_DOC">Enrollment Agreement</mat-option>
          <mat-option [value]="documentType.RENTAL_INSTRUMENT_DOC">Instrument Rental Agreement</mat-option>
        </mat-select>
        <mat-error>
          <app-error-messages [control]="documentFormGroup.controls.documentType"></app-error-messages>
        </mat-error>
      </mat-form-field>
      <ng-container
        [ngTemplateOutlet]="
          documentFormGroup.controls.filePath.value ? uploadedFileTemplate : fileUploadTemplate
        "></ng-container>
    </form>
  </div>
</div>

<ng-template #fileUploadTemplate>
  <div class="drag-drop-file" appDnd [maxFileSize]="constants.maxFileSizes.MAX_FILE_SIZE_5MB" [allowedExtensions]="allowedExtension" (fileDropped)="onFileDropped($event)" (invalidFile)="onInvalidFile($event)">
    <img [src]="constants.staticImages.icons.fileUpload" alt="" />
    <div class="drag-drop-file-content">
      <div>
        Drop your file here, or
        <span class="file-input">
          <span (click)="fileInput.click()">Browse</span>
          <input
            type="file"
            accept=".pdf"
            #fileInput
            [hidden]="true"
            [multiple]="false"
            (change)="onFileSelected($event)" />
        </span>
        <div class="text-gray">PDF (5 MB)</div>
      </div>
      @if (isShowUploadSpinner) {
        <ng-container [ngTemplateOutlet]="showUploadSpinner"></ng-container>
      }
    </div>
  </div>
  <mat-error class="ms-3">
    <app-error-messages [control]="documentFormGroup.controls.filePath"></app-error-messages>
  </mat-error>
</ng-template>

<ng-template #uploadedFileTemplate>
  <div class="drag-drop-file justify-content-between">
    <div>
      <img [src]="constants.staticImages.images.pdfImage" width="40" height="40" alt="" />
      {{ documentFormGroup.controls.filePath.value }}
    </div>
    <a (click)="onRemoveUploadedFileConfirmation()">
      <mat-icon><img [src]="constants.staticImages.icons.trash" alt="" /></mat-icon>
    </a>
  </div>
</ng-template>

<ng-template #showUploadSpinner>
  <div class="loader"></div>
</ng-template>
