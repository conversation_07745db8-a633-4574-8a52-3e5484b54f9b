import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_LOCALE, MAT_DATE_FORMATS } from '@angular/material/core';
import { MatDatepicker, MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import moment, { Moment } from 'moment';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';

const DEPENDENCIES = {
  MODULES: [MatDatepickerModule, MatFormFieldModule, MatInputModule, FormsModule, CommonModule]
};

export const MONTH_YEAR_FORMATS = {
  parse: {
    dateInput: 'MM/YYYY'
  },
  display: {
    dateInput: 'MM/YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY'
  }
};

@Component({
  selector: 'app-month-year-picker',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    { provide: MAT_DATE_FORMATS, useValue: MONTH_YEAR_FORMATS }
  ],
  templateUrl: './month-year-picker.component.html',
  styleUrl: './month-year-picker.component.scss'
})
export class MonthYearPickerComponent extends BaseComponent implements OnInit {
  @Input() selectedYear!: Moment;
  @Output() refreshData = new EventEmitter<Moment>();

  constructor() {
    super();
  }

  ngOnInit(): void {
    this.selectedYear = moment();
  }

  chosenYearHandler(normalizedYear: Moment) {
    this.selectedYear = this.selectedYear.clone().year(normalizedYear.year());
  }

  chosenMonthHandler(normalizedMonth: Moment, datepicker: MatDatepicker<Moment>) {
    this.selectedYear = this.selectedYear.clone().month(normalizedMonth.month());
    this.refreshData.emit(this.selectedYear);
    datepicker.close();
  }
}
