<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="title">Group Class Details</div>
    <div class="action-btn-wrapper">
      <button
        mat-raised-button
        color="accent"
        class="mat-accent-btn back-btn"
        type="button"
        (click)="closeViewSideNavFun()">
        Close
      </button>
      <button
        mat-raised-button
        color="accent"
        class="mat-accent-btn back-btn"
        type="button"
        (click)="deleteGroupConfirmation()">
        Delete
      </button>
      <button
        mat-raised-button
        *ngIf="selectedTabOption === 'Upcoming Classes'"
        color="primary"
        class="mat-primary-btn"
        type="button"
        (click)="navigateToEdit()">
        Edit
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : viewGroupClass"></ng-container>
  </div>
</div>

<ng-template #viewGroupClass>
  <div class="group-class-detail mb-3">
    <div class="group-class-content-wrapper title">
      <div class="name">
        {{ groupClassDetail?.groupClassScheduleSummary?.groupClassName | titlecase }}
        ({{ schedulerService.getAgeLabelFromValue(groupClassDetail?.groupClassScheduleSummary?.ageGroup) }})
      </div>
      <div class="primary-color">${{ groupClassDetail?.groupClassScheduleSummary?.price }}</div>
    </div>
    <div class="group-class-content-wrapper">
      <div>
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.questionnaire" alt="" />
          <div class="info-label">Details</div>
        </div>
        <div class="info-content w-570">
          {{ groupClassDetail?.groupClassScheduleSummary?.description | dashIfEmpty }}
        </div>
      </div>
    </div>
    <div class="group-class-content-wrapper">
      <div>
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.calendarIcon" alt="" />
          <div class="info-label">Date</div>
        </div>
        <div class="info-content">
          {{ groupClassDetail?.groupClassScheduleSummary?.scheduleStartDate | localDate | date: "mediumDate" }} -
          {{ groupClassDetail?.groupClassScheduleSummary?.scheduleEndDate | localDate | date: "mediumDate" }}
        </div>
      </div>
      <div class="w-50">
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.calendarIcon" alt="" />
          <div class="info-label">Time</div>
        </div>
        <div class="info-content">
          {{ groupClassDetail?.groupClassScheduleSummary?.scheduleStartTime | localDate | date: "shortTime" }} -
          {{ groupClassDetail?.groupClassScheduleSummary?.scheduleEndTime | localDate | date: "shortTime" }}
        </div>
      </div>
    </div>

    <div class="group-class-content-wrapper">
      <div>
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.skill" alt="" />
          <div class="info-label">Skill Level</div>
        </div>
        <div class="info-content">{{ groupClassDetail?.groupClassScheduleSummary?.skillType }}</div>
      </div>
      <div class="w-50">
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.repeatType" alt="" />
          <div class="info-label">Repeat Types</div>
        </div>
        <div class="info-content">
          Weekly
          <div class="dot"></div>
          Every {{ schedulerService.getDayOfWeek(groupClassDetail?.groupClassScheduleSummary?.scheduleDays!) }}
        </div>
      </div>
    </div>

    <div class="group-class-content-wrapper">
      <div>
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.location" alt="" />
          <div class="info-label">Location</div>
        </div>
        <div class="info-content">{{ groupClassDetail?.groupClassScheduleSummary?.locationName }}</div>
      </div>
      <div class="w-50">
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.home" alt="" />
          <div class="info-label">Room Name</div>
        </div>
        <div class="info-content">{{ groupClassDetail?.groupClassScheduleSummary?.roomName | dashIfEmpty }}</div>
      </div>
    </div>

    <div class="group-class-content-wrapper">
      <div>
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.safeHome" alt="" />
          <div class="info-label">Client Capacity</div>
        </div>
        <div class="info-content">{{ groupClassDetail?.groupClassScheduleSummary?.studentCapacity }}</div>
      </div>
      <div class="w-50">
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.calendarIcon" alt="" />
          <div class="info-label">Enrollment Cutoff Date</div>
        </div>
        <div class="info-content">
          {{ groupClassDetail?.groupClassScheduleSummary?.enrollLastDate | localDate | date: "mediumDate" }}
        </div>
      </div>
    </div>

    <div class="group-class-content-wrapper">
      <div>
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.status" alt="" />
          <div class="info-label">Revenue Category</div>
        </div>
        <div class="info-content">{{ groupClassDetail?.groupClassScheduleSummary?.category }}</div>
      </div>
      @if (groupClassDetail?.groupClassScheduleSummary?.isWaitlistAvailable) {
        <div class="w-50">
          <div class="group-class-content">
            <img [src]="constants.staticImages.icons.checkSquare" alt="" />
            <div class="waitlist">Waitlist Available</div>
          </div>
        </div>
      }
    </div>
  </div>

  <div class="instructor-details">
    <div class="group-class-content-wrapper title">
      <div class="name">Instructor Details</div>
    </div>
    <div class="group-class-content-wrapper">
      <div>
        <div class="group-class-content">
          <img [src]="constants.staticImages.images.profileImgPlaceholder" alt="" />
          <div class="user-label">{{ groupClassDetail?.instructorDetails?.name }}</div>
        </div>
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.phone" alt="" />
          <div class="user-label">{{ groupClassDetail?.instructorDetails?.phoneNumber }}</div>
        </div>
      </div>
      <div class="w-50">
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.instrumentIcon" alt="" />
          @if ((groupClassDetail?.instructorDetails)!.instruments.length) {
            <div class="user-label">
              {{ (groupClassDetail?.instructorDetails)!.instruments[0].instrumentName | dashIfEmpty }}
              <span class="primary-color">{{
                (groupClassDetail?.instructorDetails)!.instruments[0].instrumentGrade | dashIfEmpty
              }}</span>
            </div>
          }
        </div>
        <div class="group-class-content">
          <img [src]="constants.staticImages.icons.email" alt="" />
          <div class="user-label">{{ groupClassDetail?.instructorDetails?.email }}</div>
        </div>
      </div>
    </div>
  </div>

  @if (groupClassDetail?.scheduleStudentDetails?.length) {
    <div class="instructor-details">
      <div class="group-class-content-wrapper title">
        <div class="name">Registered Clients</div>
      </div>
      @for (studentDetail of groupClassDetail?.scheduleStudentDetails; track $index) {
        <div class="group-class-content-wrapper mb-0">
          <div>
            <div class="group-class-content mb-0">
              <img [src]="constants.staticImages.icons.profileIcon" alt="" />
              <div class="user-label">{{ studentDetail.studentName }}</div>
            </div>
            <div class="group-class-content mb-0 ms-4">
              <div class="info-label me-1">Account Manager:</div>
              <div class="user-label">{{ studentDetail.accountManagerName }}</div>
            </div>
          </div>
        </div>
        <div class="group-class-content-wrapper ms-4">
          <div class="group-class-content mb-0">
            <img [src]="constants.staticImages.icons.email" alt="" />
            <div class="user-label">{{ studentDetail.accountManagerEmail }}</div>
          </div>
          <div class="group-class-content mb-0 w-50">
            <img [src]="constants.staticImages.icons.phone" alt="" />
            <div class="user-label">{{ studentDetail.accountManagerPhoneNo }}</div>
          </div>
        </div>
        <div class="dotted-divider" *ngIf="$index < groupClassDetail?.scheduleStudentDetails?.length! - 1"></div>
      }
    </div>
  }
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
