<mat-sidenav-container class="example-container" [hasBackdrop]="true">
  <mat-sidenav
    [opened]="isPDFViewerSideNavOpen"
    mode="over"
    position="end"
    fixedInViewport="true"
    class="xl-sidebar"
    [disableClose]="true">
    @if (isPDFViewerSideNavOpen) {
      <app-pdf-viewer
        [selectedPlanDocument]="selectedPlanDocument"
        (closeSideNav)="isPDFViewerSideNavOpen = false"></app-pdf-viewer>
    }
  </mat-sidenav>

  <mat-sidenav-content>
    <!-- <div class="header-tab-only-btn">
      <button mat-raised-button color="primary" class="mat-primary-btn action-btn" type="button">Add Plan</button>
    </div> -->

    <div class="auth-page-with-header">
      <div class="search-and-count-wrapper-auth">
        <div class="search-and-count-wrapper">
          <div class="total-users">
            Total: <span>{{ totalCount }}</span>
          </div>
        </div>
        <div class="filter-wrapper">
          <mat-form-field class="search-bar-wrapper">
            <mat-select [(ngModel)]="filters.planTypeFilter" (selectionChange)="getPlanDetail()">
              <mat-option [value]="all.ALL">All Plan Types</mat-option>
              <mat-option *ngFor="let planType of planTypes | enumToKeyValue" [value]="planType.value">
                {{ planType.key | titlecase }}
              </mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field class="search-bar-wrapper">
            <mat-select [(ngModel)]="filters.DurationFilter" (selectionChange)="getPlanDetail()">
              <mat-option [value]="all.ALL">All Durations</mat-option>
              <mat-option *ngFor="let duration of durations | enumToKeyValue" [value]="duration.value">
                {{ duration.value }} Minutes
              </mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field class="search-bar-wrapper">
            <mat-select [(ngModel)]="filters.VisitsPerWeekFilter" (selectionChange)="getPlanDetail()">
              <mat-option [value]="all.ALL">All Visits per week</mat-option>
              <mat-option *ngFor="let visit of visits | enumToKeyValue" [value]="visit.value">
                {{ visit.value }} Visit
              </mat-option>
            </mat-select>
          </mat-form-field>
          <mat-form-field class="search-bar-wrapper">
            <mat-select [(ngModel)]="filters.planFilter" (selectionChange)="getPlanDetail()">
              <mat-option [value]="all.ALL">All Plans</mat-option>
              <mat-option *ngFor="let plan of plans | enumToKeyValue" [value]="plan.value">
                {{ plan.key | titlecase }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : planLists"></ng-container>
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>

<ng-template #planLists>
  <ng-container [ngTemplateOutlet]="planDetails.length ? planList : noDataFound"></ng-container>
</ng-template>

<ng-template #planList>
  <div class="plan-list">
    @for (planDetail of planDetails; track $index) {
      <div class="o-card mb-2">
        <div class="o-card-body">
          <div class="plan-summary">
            <div class="title">
              @switch (true) {
                @case (planDetail.planSummary.isRentalPlan) {
                  {{ planDetail.planSummary.instrumentName }} Rental Plan {{ planDetail.planSummary.isPlanWithInsurance ? 'With Insurance' : 'Without Insurance' }}
                }
                @case (planDetail.planSummary.isDDDPlan) {
                  DDD Plan ({{ planDetail.planSummary.plandetails.items[0].planDetail.duration }})
                }
                @default {
                  Weekly {{ planDetail.planSummary.isEnsembleAvailable ? 'Ensemble' : 'Music' }} Lessons ({{ planSummaryService.getPlanType(planDetail.planSummary.planType) }}-{{
                    planSummaryService.getPlanSummary(planDetail.planSummary.plandetails.items)
                  }})                  
                }
              }
            </div>
            <div class="plan-content">
              @if (!planDetail.planSummary.isRentalPlan && !planDetail.planSummary.isDDDPlan) {
                <div>
                  {{ planSummaryService.getTotalVisits(planDetail.planSummary.plandetails.items) }} visits per week
                </div>
                <div class="dot"></div>
              }
              <div>{{ planDetail.planSummary.plan === plans.RECURRING ? "Recurring" : "Custom" }} plan</div>
              <div class="dot"></div>
              <div class="text-black">
                Rev. Category <span class="primary-color">{{ planDetail.planSummary.categoryName }}</span>
              </div>
            </div>
          </div>
          <div class="plan-pricing">
            <div class="title text-end">
              ${{ planDetail.planSummary.planPrice }} /<span class="fw-normal"> Month</span>
            </div>
            <button
              mat-raised-button
              color="primary"
              class="mat-primary-btn action-btn"
              type="button"
              (click)="openPDFViewer(planDetail.planSummary.fullFilePath)">
              View PDF
            </button>
          </div>
        </div>
      </div>
    }
  </div>
</ng-template>

<ng-template #noDataFound>
  <div class="no-data-found-card">
    <h3>NO DATA FOUND!</h3>
  </div>
</ng-template>

<ng-template #showLoader>
  <app-content-loader></app-content-loader>
</ng-template>
