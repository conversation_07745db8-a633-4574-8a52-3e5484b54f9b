<div class="o-sidebar-wrapper">
  <div class="o-sidebar-header">
    <div class="title">{{ selectedDeskManagerDetails ? "Edit" : "Add" }} Desk Manager</div>
    <div class="action-btn-wrapper">
      <button
        mat-raised-button
        color="accent"
        class="mat-accent-btn back-btn"
        type="button"
        (click)="closeSideNavFun()">
        Close
      </button>
      <button
        mat-raised-button
        color="primary"
        class="mat-primary-btn"
        type="button"
        (click)="onSubmit()"
        [appLoader]="showBtnLoader">
        Save
      </button>
    </div>
  </div>
  <div class="divider"></div>
  <div class="o-sidebar-body">
    <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : deskManagerForm"></ng-container>
  </div>
</div>

<ng-template #deskManagerForm>
  <form [formGroup]="deskManagerFormGroup">
    <div class="field-wrapper field-with-mat-inputs">
      <label class="required">Full Name</label>
      <mat-form-field class="mat-select-custom instructor">
        <input matInput placeholder="Enter Name" formControlName="name" />
        <mat-error>
          <app-error-messages [control]="deskManagerFormGroup.controls.name"></app-error-messages>
        </mat-error>
      </mat-form-field>
    </div>
    <div class="field-wrapper field-with-mat-inputs">
      <label class="required">Email</label>
      <mat-form-field class="mat-select-custom instructor">
        <input matInput placeholder="Enter Email" formControlName="email" />
        <mat-error>
          <app-error-messages [control]="deskManagerFormGroup.controls.email"></app-error-messages>
        </mat-error>
      </mat-form-field>
    </div>
    <div class="field-wrapper field-with-mat-inputs">
      <label class="required">Phone Number</label>
      <mat-form-field class="mat-select-custom instructor">
        <input
          matInput
          placeholder="Enter Phone Number"
          formControlName="phoneNumber"
          [mask]="constants.masking.phoneNumberMask" />
        <mat-error>
          <app-error-messages [control]="deskManagerFormGroup.controls.phoneNumber"></app-error-messages>
        </mat-error>
      </mat-form-field>
    </div>
    <div class="field-wrapper field-with-mat-inputs">
      <label class="required">Date of Birth</label>
      <div class="w-100">
        <mat-form-field class="mat-start-date">
          <input
            matInput
            [matDatepicker]="picker"
            (click)="picker.open()"
            formControlName="dateOfBirth"
            placeholder="Enter Date of Birth"
            [max]="maxDate" />
          <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-datepicker #picker></mat-datepicker>
          <mat-error>
            <app-error-messages [control]="deskManagerFormGroup.controls.dateOfBirth"></app-error-messages>
          </mat-error>
        </mat-form-field>
      </div>
    </div>
        <div class="field-wrapper field-with-mat-inputs">
      <label class="required">Address</label>
      <mat-form-field class="instructor">
        <textarea matInput placeholder="Enter Address" formControlName="address"></textarea>
        <mat-error>
          <app-error-messages [control]="deskManagerFormGroup.controls.address"></app-error-messages>
        </mat-error>
      </mat-form-field>
    </div>
    <div class="field-wrapper field-with-mat-inputs">
      <label class="required">State</label>
      <mat-form-field class="mat-select-custom instructor">
        <mat-select formControlName="stateId" placeholder="Select State">
            <mat-option *ngFor="let state of states" [value]="state.id">
              {{ state.name }}
            </mat-option>
        </mat-select>
        <mat-error>
          <app-error-messages [control]="deskManagerFormGroup.controls.stateId"></app-error-messages>
        </mat-error>
      </mat-form-field>
    </div>
    <div class="field-wrapper field-with-mat-inputs">
      <label class="required">City</label>
      <mat-form-field class="mat-select-custom instructor">
        <input matInput placeholder="Enter City" formControlName="city" />
        <mat-error>
          <app-error-messages [control]="deskManagerFormGroup.controls.city"></app-error-messages>
        </mat-error>
      </mat-form-field>
    </div>
    <div class="field-wrapper field-with-mat-inputs">
      <label class="required">Zip Code</label>
      <mat-form-field class="mat-select-custom instructor">
        <input matInput placeholder="Enter Zip Code" formControlName="zipCode" />
        <mat-error>
          <app-error-messages [control]="deskManagerFormGroup.controls.zipCode"></app-error-messages>
        </mat-error>
      </mat-form-field>
    </div>

    <!-- to be used -->
    <!-- <div>
      <div class="instructor-instruments-wrapper" formArrayName="leaveBalances">
        <div
          class="instructor-instruments-form"
          *ngFor="let leave of getInstructorFormArray('leaveBalances').controls; let i = index"
          [formGroupName]="i"
        >
          <div class="mobile">
            @if (getInstructorFormArray('leaveBalances').length > 1) {
              <div class="content">
                {{ "Leave Type " + (i + 1) }}
              </div>
            }
          </div>
          <div class="instructor-instruments">
            <div class="field-wrapper field-with-mat-inputs">
              <label>
                {{ leave.get('leaveType')?.value === leaveTypes.PAID ? 'Paid Time Off' : 'Unpaid Time Off' }}
              </label>
            </div>
            <mat-form-field class="content" *ngIf="leave.get('leaveType')?.value === leaveTypes.PAID">
              <mat-label>Total Leave Days</mat-label>
              <input matInput type="number" formControlName="totalLeaveDays" (input)="getRemainingLeaveDays(i)" />
              <mat-error>
                <app-error-messages
                  [control]="
                    getInstructorFormArray('leaveBalances').controls[i].get('totalLeaveDays')
                  "></app-error-messages>
              </mat-error>
            </mat-form-field>
            <mat-form-field class="content">
              <mat-label>Used Leave Days</mat-label>
              <input matInput type="number" formControlName="usedLeaveDays" (input)="getRemainingLeaveDays(i)" />
              <mat-error class="used-leave-error">
                <app-error-messages
                  [control]="
                    getInstructorFormArray('leaveBalances').controls[i].get('usedLeaveDays')
                  "></app-error-messages>
              </mat-error>
            </mat-form-field>
            <mat-form-field class="content" *ngIf="leave.get('leaveType')?.value === leaveTypes.PAID">
              <mat-label>Remaining Leave Days</mat-label>
              <input matInput [value]="getRemainingLeaveDays(i)" disabled />
            </mat-form-field>
          </div>
        </div>
      </div>
    </div> -->
    <div class="mt-2">
      <div class="title-wrapper">
        <div>Select Location and Availability</div>
      </div>
      <div
        class="location-availability-wrapper"
        *ngFor="let instructor of getInstructorFormArray('deskManagerAvailabilityAndLocations').controls; let i = index"
        formArrayName="deskManagerAvailabilityAndLocations">
        <div class="location-availability-header mobile">
          @if (getInstructorFormArray("deskManagerAvailabilityAndLocations").length > 1) {
            <div class="content">
              {{ i + 1 + ". Location & Availability" }}
            </div>
            <div class="trash-icon" (click)="confirmationPopup(i)">
              <img [src]="constants.staticImages.icons.redTrash" alt="" />
              <div class="ms-2">Delete</div>
            </div>
          }
        </div>
        <div class="location-availability-form pb-0">
          <div [formGroupName]="i">
            <div class="field-wrapper field-with-mat-inputs w-100">
              <label class="required">Select Location</label>
              <div class="field-content">
                <mat-form-field class="w-100 mat-select-custom">
                  <mat-select formControlName="locationsId" placeholder="Select Location">
                    <mat-option *ngFor="let location of locations" [value]="location.schoolLocations.id">
                      {{ location.schoolLocations.locationName }}
                    </mat-option>
                  </mat-select>
                  <mat-error>
                    <app-error-messages
                      [control]="
                        getInstructorFormArray('deskManagerAvailabilityAndLocations').controls[i].get('locationsId')
                      "></app-error-messages>
                  </mat-error>
                </mat-form-field>
              </div>
            </div>

            <div class="field-wrapper field-with-mat-inputs">
              <label class="required">Start Date</label>
              <div class="w-100">
                <mat-form-field class="mat-start-date">
                  <input
                    matInput
                    [matDatepicker]="picker"
                    (click)="picker.open()"
                    formControlName="availableStartDate"
                    placeholder="Select Start Date"
                    [min]="getMinStartDate(i)"
                    (dateChange)="setDate('availableStartDate', i); resetValues(i)" />
                  <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                  <mat-datepicker #picker></mat-datepicker>
                  <mat-error>
                    <app-error-messages
                      [control]="
                        getInstructorFormArray('deskManagerAvailabilityAndLocations').controls[i].get(
                          'availableStartDate'
                        )
                      "></app-error-messages>
                  </mat-error>
                </mat-form-field>
              </div>
            </div>

            @if (getInstructorFormArray("deskManagerAvailabilityAndLocations").controls[i].get("availableStartDate")?.value) {
              <div class="field-wrapper field-with-mat-inputs">
                <label class="required">Start Time - End Time</label>
                <div class="field-content">
                  <div class="w-100">
                    <ngx-timepicker-field
                      formControlName="availableStartTime"
                      [minutesGap]="constants.fiveMinutesGap"
                      [defaultTime]="getFormattedStartTime(i)"
                      (timeChanged)="
                        setTime('availableStartTime', i); setFormControlValue('availableEndTime', '', i)
                      "></ngx-timepicker-field>
                    <mat-error>
                      <app-error-messages
                        [control]="
                          getInstructorFormArray('deskManagerAvailabilityAndLocations').controls[i].get(
                            'availableStartTime'
                          )
                        "></app-error-messages>
                    </mat-error>
                  </div>
                  <div class="dash mb-4">-</div>
                  <div class="w-100">
                    <ngx-timepicker-field
                      formControlName="availableEndTime"
                      [minutesGap]="constants.fiveMinutesGap"
                      [defaultTime]="getFormattedEndTime(i)"
                      (timeChanged)="setTime('availableEndTime', i)"></ngx-timepicker-field>
                    <mat-error>
                      <app-error-messages
                        [control]="
                          getInstructorFormArray('deskManagerAvailabilityAndLocations').controls[i].get(
                            'availableEndTime'
                          )
                        "></app-error-messages>
                    </mat-error>
                  </div>
                </div>
              </div>

              <div class="field-wrapper field-with-mat-inputs">
                <label class="required">Availability Repeat Types</label>
                <div>
                  <div class="single-btn-select-wrapper">
                    @for (availability of availabilityTypes | enumToKeyValue; track $index) {
                      <div
                        [ngClass]="{
                          active:
                            getInstructorFormArray('deskManagerAvailabilityAndLocations').controls[i].get(
                              'availabilityType'
                            )?.value === availability.value
                        }"
                        class="select-btn"
                        (click)="setFormControlValue('availabilityType', availability.value, i)">
                        {{ availability.key.replace("_", " ") | titlecase }}
                      </div>
                    }
                  </div>
                  <mat-error class="mat-error-position">
                    <app-error-messages
                      [control]="
                        getInstructorFormArray('deskManagerAvailabilityAndLocations').controls[i].get('availabilityType')
                      "></app-error-messages>
                  </mat-error>
                </div>
              </div>
            }

            @if (
              getInstructorFormArray("deskManagerAvailabilityAndLocations").controls[i].get("availabilityType")
                ?.value &&
              getInstructorFormArray("deskManagerAvailabilityAndLocations").controls[i].get("availableStartDate")
                ?.value &&
              getInstructorFormArray("deskManagerAvailabilityAndLocations").controls[i].get("availabilityType")
                ?.value !== availabilityTypes.NO_REPEATS
            ) {
              <div class="field-wrapper field-with-mat-inputs">
                <label class="required">Stop Repeating</label>
                <div>
                  <div class="btn-typed-options-wrapper">
                    <div
                      [ngClass]="{
                        'btn-typed-option': true,
                        active: getInstructorFormArray('deskManagerAvailabilityAndLocations').controls[i].get(
                          'neverEnd'
                        )?.value
                      }"
                      (click)="
                        setFormControlValue(
                          'availableEndDate',
                          getFiveYearLaterDate(
                            getInstructorFormArray('deskManagerAvailabilityAndLocations').controls[i].get(
                              'availableStartDate'
                            )?.value
                          ),
                          i
                        )
                      ">
                      Never End
                    </div>
                    <div>
                      <mat-form-field class="mat-start-date end-date">
                        <input
                          matInput
                          [matDatepicker]="pickerEnd"
                          (click)="pickerEnd.open()"
                          formControlName="availableEndDate"
                          placeholder="Select End Date"
                          [min]="
                            getInstructorFormArray('deskManagerAvailabilityAndLocations').controls[i].get(
                              'availableStartDate'
                            )?.value ?? maxDate
                          "
                          (dateChange)="setDate('availableEndDate', i)" />
                        <mat-datepicker-toggle matSuffix [for]="pickerEnd"></mat-datepicker-toggle>
                        <mat-datepicker #pickerEnd></mat-datepicker>
                      </mat-form-field>
                    </div>
                  </div>
                  <mat-error class="mat-error-position">
                    <app-error-messages
                      [control]="
                        getInstructorFormArray('deskManagerAvailabilityAndLocations').controls[i].get(
                          'availableEndDate'
                        )
                      "></app-error-messages>
                  </mat-error>
                </div>
              </div>

              @if (
                getInstructorFormArray("deskManagerAvailabilityAndLocations").controls[i].get("availabilityType")
                  ?.value === availabilityTypes.WEEKLY
              ) {
                <div class="field-wrapper field-with-mat-inputs">
                  <label class="required">Select Weekday(s)</label>
                  <div>
                    <div class="single-btn-select-wrapper">
                      @for (day of constants.daysOfTheWeek; track $index) {
                        <div
                          [ngClass]="{ active: isDaySelected(day.value, i) }"
                          class="select-btn"
                          (click)="setDaysOfWeek(day.value, i)">
                          {{ day.label }}
                        </div>
                      }
                    </div>
                    <mat-error class="mat-error-position">
                      <app-error-messages
                        [control]="
                          getInstructorFormArray('deskManagerAvailabilityAndLocations').controls[i].get('availableDays')
                        "></app-error-messages>
                    </mat-error>
                  </div>
                </div>
              }
            }
          </div>
        </div>
      </div>
    </div>
    <div class="add-instructor-instruments-wrapper" (click)="addNewLocationAndInstructorAvailability()">
      + Add New Location And Availability
    </div>

    <div class="mt-4" *ngIf="showLeaveBalance()">
      <div class="title-wrapper">
        <div>Add Leave Balance</div>
      </div>
      <div
        class="location-availability-wrapper" formArrayName="leaveBalances"
        *ngIf="getDaysForTimeOff().length">
        <div class="location-availability-form pb-0">
          <div>
            <div class="time-off-content">
              <div class="weekly-time-off">
                @for (day of getDaysForTimeOff(); track day.value) {
                  <div class="day-time-off">
                    <div class="day-label">{{ day.key }}</div>
                    <div *ngIf="getLeaveData(day.value) as leaveData">
                    <div class="time-off-inputs">
                        <mat-form-field class="time-off-field">
                        <mat-label>Total Paid Leave</mat-label>
                        <input matInput type="number" min="0" step="1"
                               [value]="leaveData.totalLeaveDays"
                               (input)="setLeaveData(leaveData, 'totalLeaveDays', $event); capUsedLeave(leaveData, $event)" />
                      </mat-form-field>
                      <mat-form-field class="time-off-field">
                        <mat-label>Used Paid Leave</mat-label>
                        <input matInput type="number" min="0" step="1"
                               [value]="leaveData.usedLeaveDays"
                               (input)="setLeaveData(leaveData, 'usedLeaveDays', $event); capUsedLeave(leaveData, $event)" />
                      </mat-form-field>
                      <mat-form-field class="time-off-field">
                        <mat-label>Remaining Paid Leave</mat-label>
                        <input matInput type="number" min="0" step="1"
                               [value]="leaveData.remainingLeaveDays"
                               disabled />
                      </mat-form-field>
                      <!-- to be used -->
                      <!-- <mat-form-field class="time-off-field">
                        <mat-label>Used Unpaid Leave</mat-label>
                        <input matInput type="number" min="0" step="1"
                               [value]="leaveData.unpaidUsedLeaveDays"
                               (input)="getHello2(leaveData, 'unpaidUsedLeaveDays', $event)" />
                      </mat-form-field> -->
                      </div>
                    </div>
                  </div>
                }
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
