<ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : viewProductTemplate"></ng-container>

<ng-template #viewProductTemplate>
  <div class="o-sidebar-wrapper">
    <div class="o-sidebar-header">
        <div class="title">Product Details</div>
      <div class="action-btn-wrapper">
        <button
          mat-raised-button
          color="accent"
          class="mat-accent-btn back-btn"
          type="button"
          (click)="closeViewSideNavFun()">
          Close
        </button>
        @if (!isFromMembers) {
          <button
            mat-raised-button
            color="accent"
            class="mat-accent-btn back-btn"
            type="button"
            (click)="onDelete()">
            Delete
          </button>
          <button
            mat-raised-button
            color="primary"
            class="mat-primary-btn"
            type="button"
            (click)="onEdit()"
            [appLoader]="showBtnLoader">
            Edit
          </button>
        }
        @else {
          <button
            mat-raised-button
            color="primary"
            class="mat-primary-btn"
            type="button"
            (click)="addToCartFun(); closeViewSideNavFun()"
            [disabled]="selectedProduct?.status === productStatus.OUT_OF_STOCK"
            [appLoader]="showBtnLoader">
            @if (selectedProduct?.status === productStatus.OUT_OF_STOCK) {
              <img [src]="constants.staticImages.icons.shoppingBagWithLine" alt="" class="img-bag disabled-bag"> Out of Stock
           } @else {
              <img [src]="constants.staticImages.icons.shoppingBag" alt="" class="img-bag"> Add to Cart
           }
          </button>
        }
      </div>
    </div>
    <div class="divider"></div>
    <div class="o-sidebar-body">
        <div class="product-info mb-3">
            <div class="row">
              <div class="col-md-4" *ngIf="productDetail.productImage">
                @if (isImageOrPDF(productDetail.productImage) === 'pdf') {
                  <iframe
                    [src]="productDetail.productImage | safe"
                    frameborder="1"
                    height="250px"
                    width="100%" title="card"
                  ></iframe>
                }
                @else {
                  <img [src]="productDetail.productImage" alt="" class="product-img" />
                }
              </div>
      
              <div class="col-md-8">
                  <div class="product-detail">
                      <div class="product-name">{{ productDetail.productName | titlecase }}</div>
                      <div class="price">${{ productDetail.productPrice }}</div>
                      <div class="desc">{{ productDetail.productDescription }}</div>
                  </div>
              </div>
            </div>
        </div>
        <div class="product-info mb-3">
            <div class="title">Selected Revenue Category</div>
            <div class="content">{{ productDetail.productRevenueCategoryName }}</div>
        </div>
        <div class="product-info">
            <div class="title mb-2">Stock Details</div>
            <div class="o-table">
                <div class="o-row o-header">
                  <div class="o-cell first-cell text-start">Location</div>
                  <div class="o-cell">Total Qty</div>
                  <div class="o-cell">Sold</div>
                  <div class="o-cell">Available Qty</div>
                </div>
                <div class="dotted-divider"></div>
                <div class="content">
                    @for (storeProduct of productDetail.storeProductQuantities; track $index) {
                        <div class="o-row">
                            <div
                            class="o-cell first-cell text-gray text-start">
                            <img [src]="constants.staticImages.icons.location" alt="">
                            {{ storeProduct.locationName }}
                          </div>
                          <div class="o-cell text-black">{{ storeProduct.totalQuantity }}</div>
                          <div class="o-cell text-black">{{ storeProduct.totalQuantity - storeProduct.availableQuantity }}</div>
                          <div class="o-cell text-black">{{ storeProduct.availableQuantity }}</div>
                        </div>
                    
                        @if (!$last) {
                          <div class="dotted-divider"></div>
                        }
                      }
                </div>
            </div>
        </div>
    </div>
  </div>
</ng-template>

<ng-template #showLoader>
  <div class="page-loader-wrapper">
    <app-content-loader></app-content-loader>
  </div>
</ng-template>
