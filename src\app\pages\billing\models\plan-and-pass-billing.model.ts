import { PlanDetailWrapper } from '../../settings/pages/plan/models';

export interface UserBillingTransactionsResponse {
  userBillingTransactions: BillingDetails;
}

export interface BillHistoryRes {
  userTransactionDetails: BillingDetails;
}

export interface BillingDetails {
  id: number;
  billId: string;
  billDate: string;
  billAmount: number;
  billPaymentStatus: number;
  billPaymentDate: string;
  billDiscountedAmount: number;
  planInstrument: string | null;
  instrumentName: string | null;
  planType: number;
  planDetails: PlanDetailWrapper[];
  scheduleStartTime: string;
  scheduleEndTime: string;
  scheduleStartDate: string;
  scheduleEndDate: string;
  scheduleType: number;
  transactionId: string;
  dependentName: string;
  dependentInformationId: number;
  dependentBillingDetails: DependentBillingDetail[];
  groupClassName: string;
  transactionType: number;
  chequeNumber: string | null;
  classType: number;
  totalAmount: number;
  planId: number;
  ensembleClassName: string;
  summerCampName: string;
  paidAmount: number;
  discountedAmount: number;
  accountManagerId: number;
  accountManagerName: string;
  accountManagerEmail: string;
  accountManagerPhone: string;
  isRefunded: boolean;
  refundTransactionDetails: RefundTransactionDetails[];
  bankAccountNumber: string;
  bankRoutingNumber: string;
  ccType: string;
  ccNum: string;
  ccExpiry: string; 
  reason: string;
  isRentalPlan: boolean;
  isDDDPlan: boolean;
  isPlanWithInsurance: boolean;
}

export interface DependentBillingDetail {
  dependentInformationId: number;
  dependentName: string;
  scheduleType: number;
  planInstrument: string;
  planType: number;
  planAmount: number;
  planDetails: PlanDetailWrapper[];
  scheduleStartTime: string;
  scheduleEndTime: string;
  scheduleStartDate: string;
  scheduleEndDate: string;
  totalAmount: number;
  paidAmount: number;
  discountedAmount: number;
  transactionType: number;
  chequeNumber: string | null;
  planStartDate: string;
  planEndDate: string;
  planId: number;
  isRentalPlan: boolean;
  isDDDPlan: boolean;
  isPlanWithInsurance: boolean;
  isEnsembleAvailable: boolean;
  instrumentName: string;
}

export interface RefundTransactionDetails {
  id: number;
  customerVoultId: string;
  transactionId: string;
  refundAmount: number;
  refundDate: string;
  transactionStatus: string;
}

export interface PlanAndPassBillingFilters {
  statusFilter: number;
  startDateFilter: string;
  endDateFilter: string;
}

export enum BillStatus {
  OPEN = 1,
  CLOSED = 2,
  FAILED = 3
}

export enum RefundStatus {
  SUCCESS = 'success',
  FAILED = 'failed'
}
